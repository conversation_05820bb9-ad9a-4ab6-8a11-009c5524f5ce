'use client'

import { useEffect, useState } from 'react'

export default function GoogleMapsTest() {
  const [status, setStatus] = useState('Checking...')
  const [apiKey, setApiKey] = useState('')
  const [geocodeTest, setGeocodeTest] = useState('')

  useEffect(() => {
    const key = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY
    setApiKey(key || 'Not found')

    if (!key) {
      setStatus('❌ API key not found')
      return
    }

    // Test the API key with a simple geocoding request
    testApiKey(key)

    // Test if we can load the Google Maps API
    const script = document.createElement('script')
    script.src = `https://maps.googleapis.com/maps/api/js?key=${key}&libraries=places`
    script.onload = () => {
      setStatus('✅ Google Maps API loaded successfully')
      console.log('Google Maps API loaded, testing map creation...')
      testMapCreation()
    }
    script.onerror = (error) => {
      console.error('Script loading error:', error)
      setStatus('❌ Failed to load Google Maps API - Check console for details')
    }

    document.head.appendChild(script)

    return () => {
      try {
        document.head.removeChild(script)
      } catch (e) {
        // Script might already be removed
      }
    }
  }, [])

  const testApiKey = async (key: string) => {
    try {
      const response = await fetch(
        `https://maps.googleapis.com/maps/api/geocode/json?address=Brisbane,Australia&key=${key}`
      )
      const data = await response.json()

      if (data.status === 'OK') {
        setGeocodeTest('✅ Geocoding API working')
      } else {
        setGeocodeTest(`❌ Geocoding failed: ${data.status} - ${data.error_message || 'Unknown error'}`)
      }
    } catch (error) {
      setGeocodeTest(`❌ Geocoding request failed: ${error.message}`)
    }
  }

  const testMapCreation = () => {
    try {
      if (typeof google !== 'undefined' && google.maps) {
        // Try to create a simple map
        const mapDiv = document.createElement('div')
        mapDiv.style.width = '100px'
        mapDiv.style.height = '100px'
        document.body.appendChild(mapDiv)

        const map = new google.maps.Map(mapDiv, {
          center: { lat: -27.4698, lng: 153.0251 },
          zoom: 10
        })

        console.log('Map created successfully:', map)
        setStatus('✅ Google Maps API and Map creation working')

        // Clean up
        document.body.removeChild(mapDiv)
      } else {
        setStatus('❌ Google Maps API loaded but google.maps not available')
      }
    } catch (error) {
      console.error('Map creation error:', error)
      setStatus(`❌ Map creation failed: ${error.message}`)
    }
  }

  return (
    <div className="p-4 bg-white border rounded-lg">
      <h3 className="font-semibold mb-2">Google Maps API Test</h3>
      <div className="space-y-2 text-sm">
        <p><strong>API Key:</strong> {apiKey.substring(0, 10)}...{apiKey.substring(apiKey.length - 4)}</p>
        <p><strong>Key Length:</strong> {apiKey.length}</p>
        <p><strong>Script Status:</strong> {status}</p>
        <p><strong>Geocoding Test:</strong> {geocodeTest}</p>
      </div>
    </div>
  )
}
