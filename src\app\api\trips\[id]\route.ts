import { createClient } from '@/lib/supabase/server'
import { NextRequest, NextResponse } from 'next/server'
import { withSecurity } from '@/lib/security/middleware'
import { addSecurityHeaders } from '@/lib/security/utils'
import { rateLimitConfigs } from '@/lib/security/rate-limit'
import { handleSecureError } from '@/lib/security/error-handler'

// GET /api/trips/[id] - Get specific trip with full details
export const GET = withSecurity(
  async (request: NextRequest, context: any) => {
    try {
      const params = await context.params
      const { id } = params
      const supabase = await createClient()
      const user = context.user

      const { data: trip, error } = await supabase
        .from('trips')
        .select('*')
        .eq('id', id)
        .single()

      if (error) throw error

      const response = NextResponse.json({ trip })
      return addSecurityHeaders(response)
    } catch (error) {
      return handleSecureError(error)
    }
  },
  {
    requireAuth: true,
    rateLimit: rateLimitConfigs.api,
    auditLog: true
  }
)

// PUT /api/trips/[id] - Update trip
export const PUT = withSecurity(
  async (request: NextRequest, context: any) => {
    try {
      const params = await context.params
      const { id } = params
      const requestData = await request.json()
      const supabase = await createClient()
      const user = context.user

      const { data: trip, error } = await supabase
        .from('trips')
        .update({
          ...requestData,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single()

      if (error) throw error

      const response = NextResponse.json({ trip })
      return addSecurityHeaders(response)
    } catch (error) {
      return handleSecureError(error)
    }
  },
  {
    requireAuth: true,
    rateLimit: rateLimitConfigs.api,
    auditLog: true
  }
)

// DELETE /api/trips/[id] - Delete trip
export const DELETE = withSecurity(
  async (request: NextRequest, context: any) => {
    try {
      const params = await context.params
      const { id } = params
      const supabase = await createClient()
      const user = context.user

      const { error } = await supabase
        .from('trips')
        .delete()
        .eq('id', id)

      if (error) throw error

      const response = NextResponse.json({ success: true })
      return addSecurityHeaders(response)
    } catch (error) {
      return handleSecureError(error)
    }
  },
  {
    requireAuth: true,
    rateLimit: rateLimitConfigs.api,
    auditLog: true
  }
)
