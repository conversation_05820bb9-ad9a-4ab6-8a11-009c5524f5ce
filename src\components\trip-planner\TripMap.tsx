'use client'

import { useEffect, useRef, useState } from 'react'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Badge } from '@/components/ui/Badge'
import RouteOptimizer from './RouteOptimizer'
import { routeOptimizationService, type RouteWaypoint, type OptimizedRoute } from '@/lib/services/route-optimization-service'
import { 
  MapPinIcon, 
  PlusIcon, 
  TrashIcon, 
  SearchIcon,
  MapIcon,
  ListIcon,
  NavigationIcon
} from 'lucide-react'
import toast from 'react-hot-toast'

interface TripPin {
  id: string
  name: string
  description?: string
  latitude: number
  longitude: number
  category: 'accommodation' | 'restaurant' | 'attraction' | 'transport' | 'shopping' | 'other'
  priority: 'high' | 'medium' | 'low'
  visited: boolean
  notes?: string
  estimatedCost?: number
  estimatedDuration?: number // in minutes
}

interface TripMapProps {
  tripId: string
  destination: {
    city?: string
    country: string
    latitude?: number
    longitude?: number
  }
  pins: TripPin[]
  onPinsChange: (pins: TripPin[]) => void
  readonly?: boolean
}

const categoryColors = {
  accommodation: '#e74c3c',
  restaurant: '#f39c12',
  attraction: '#3498db',
  transport: '#9b59b6',
  shopping: '#2ecc71',
  other: '#95a5a6'
}

const categoryIcons = {
  accommodation: '🏨',
  restaurant: '🍽️',
  attraction: '🎯',
  transport: '🚗',
  shopping: '🛍️',
  other: '📍'
}

export default function TripMap({ tripId, destination, pins, onPinsChange, readonly = false }: TripMapProps) {
  const mapRef = useRef<HTMLDivElement>(null)
  const mapInstanceRef = useRef<google.maps.Map | null>(null)
  const markersRef = useRef<google.maps.Marker[]>([])
  const [isLoaded, setIsLoaded] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [viewMode, setViewMode] = useState<'map' | 'list' | 'route'>('map')
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedPin, setSelectedPin] = useState<TripPin | null>(null)
  const [showAddForm, setShowAddForm] = useState(false)
  const [optimizedRoute, setOptimizedRoute] = useState<OptimizedRoute | null>(null)
  const [newPin, setNewPin] = useState({
    name: '',
    description: '',
    category: 'attraction' as TripPin['category'],
    priority: 'medium' as TripPin['priority'],
    notes: '',
    estimatedCost: '',
    estimatedDuration: ''
  })

  const loadGoogleMapsScript = (apiKey: string): Promise<void> => {
    return new Promise((resolve, reject) => {
      // Check if script already exists
      if (document.querySelector('script[src*="maps.googleapis.com"]')) {
        resolve()
        return
      }

      const script = document.createElement('script')
      script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=places,geometry`
      script.async = true
      script.defer = true

      script.onload = () => {
        console.log('Google Maps script loaded')
        resolve()
      }

      script.onerror = (error) => {
        console.error('Failed to load Google Maps script:', error)
        reject(new Error('Failed to load Google Maps script'))
      }

      document.head.appendChild(script)
    })
  }

  useEffect(() => {
    console.log('TripMap component mounted')
    console.log('Environment API key:', process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY ? 'Present' : 'Missing')
    initializeMap()
  }, [])

  useEffect(() => {
    if (isLoaded && mapInstanceRef.current) {
      updateMapMarkers()
    }
  }, [pins, isLoaded])

  const initializeMap = async () => {
    try {
      const apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY

      if (!apiKey || apiKey.trim() === '') {
        console.log('No Google Maps API key found')
        setIsLoading(false)
        return
      }

      console.log('Initializing Google Maps with API key:', apiKey.substring(0, 10) + '...')
      console.log('API key length:', apiKey.length)

      // Try direct script loading approach
      if (typeof google === 'undefined' || !google.maps) {
        console.log('Loading Google Maps API via script...')
        await loadGoogleMapsScript(apiKey)
      }

      console.log('Google Maps API loaded successfully')

      // Verify that google.maps is available
      if (typeof google === 'undefined' || !google.maps) {
        throw new Error('Google Maps API not properly loaded')
      }

      if (!mapRef.current) {
        console.error('Map container ref not found')
        return
      }

      // Default coordinates for Brisbane, Australia if no specific coordinates
      let defaultLat = destination.latitude || -27.4698
      let defaultLng = destination.longitude || 153.0251
      let defaultZoom = 12

      // If we have city and country but no coordinates, we'll geocode later
      if (!destination.latitude && destination.city && destination.country) {
        // Use a more general zoom for geocoding
        defaultZoom = 10
      }

      console.log('Creating map with center:', { lat: defaultLat, lng: defaultLng })

      const map = new google.maps.Map(mapRef.current, {
        center: { lat: defaultLat, lng: defaultLng },
        zoom: defaultZoom,
        mapTypeControl: true,
        streetViewControl: true,
        fullscreenControl: true,
        zoomControl: true,
        styles: [
          {
            featureType: 'poi',
            elementType: 'labels',
            stylers: [{ visibility: 'on' }]
          }
        ]
      })

      console.log('Map created successfully')
      mapInstanceRef.current = map

      // Add click listener for adding new pins
      if (!readonly) {
        map.addListener('click', (event: google.maps.MapMouseEvent) => {
          if (event.latLng) {
            console.log('Map clicked at:', event.latLng.lat(), event.latLng.lng())
            handleMapClick(event.latLng.lat(), event.latLng.lng())
          }
        })
      }

      // If no destination coordinates, try to geocode the destination
      if (!destination.latitude && destination.city && destination.country) {
        console.log('Geocoding destination:', destination.city, destination.country)
        await geocodeDestination(map)
      }

      setIsLoaded(true)
      console.log('Map initialization complete')
    } catch (error) {
      console.error('Error loading Google Maps:', error)
      toast.error(`Failed to load map: ${error.message}`)
    } finally {
      setIsLoading(false)
    }
  }

  const geocodeDestination = async (map: google.maps.Map) => {
    const geocoder = new google.maps.Geocoder()
    const address = `${destination.city}, ${destination.country}`

    console.log('Geocoding address:', address)

    try {
      const result = await geocoder.geocode({ address })
      console.log('Geocoding result:', result)

      if (result.results && result.results.length > 0) {
        const location = result.results[0].geometry.location
        const lat = location.lat()
        const lng = location.lng()

        console.log('Setting map center to:', { lat, lng })
        map.setCenter({ lat, lng })
        map.setZoom(12)

        // Update destination coordinates if needed
        if (!destination.latitude) {
          destination.latitude = lat
          destination.longitude = lng
        }
      } else {
        console.warn('No geocoding results found for:', address)
        toast.error(`Could not find location: ${address}`)
      }
    } catch (error) {
      console.error('Geocoding failed:', error)
      toast.error(`Failed to locate ${address}`)
    }
  }

  const updateMapMarkers = () => {
    if (!mapInstanceRef.current) return

    // Clear existing markers
    markersRef.current.forEach(marker => marker.setMap(null))
    markersRef.current = []

    // Add markers for each pin
    pins.forEach(pin => {
      const marker = new google.maps.Marker({
        position: { lat: pin.latitude, lng: pin.longitude },
        map: mapInstanceRef.current,
        title: pin.name,
        icon: {
          url: `data:image/svg+xml;charset=UTF-8,${encodeURIComponent(createCustomMarkerSVG(pin))}`,
          scaledSize: new google.maps.Size(40, 40),
          anchor: new google.maps.Point(20, 40)
        }
      })

      // Add info window
      const infoWindow = new google.maps.InfoWindow({
        content: createInfoWindowContent(pin)
      })

      marker.addListener('click', () => {
        // Close other info windows
        markersRef.current.forEach(m => {
          const iw = (m as any).infoWindow
          if (iw) iw.close()
        })
        
        infoWindow.open(mapInstanceRef.current, marker)
        setSelectedPin(pin)
      })

      ;(marker as any).infoWindow = infoWindow
      markersRef.current.push(marker)
    })
  }

  const createCustomMarkerSVG = (pin: TripPin) => {
    const color = categoryColors[pin.category]
    const opacity = pin.visited ? 0.6 : 1
    
    return `
      <svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg">
        <circle cx="20" cy="20" r="15" fill="${color}" opacity="${opacity}" stroke="white" stroke-width="2"/>
        <text x="20" y="25" text-anchor="middle" font-size="12" fill="white">${categoryIcons[pin.category]}</text>
      </svg>
    `
  }

  const createInfoWindowContent = (pin: TripPin) => {
    return `
      <div style="max-width: 250px; padding: 8px;">
        <h3 style="margin: 0 0 8px 0; font-size: 16px; font-weight: bold;">${pin.name}</h3>
        ${pin.description ? `<p style="margin: 0 0 8px 0; font-size: 14px; color: #666;">${pin.description}</p>` : ''}
        <div style="display: flex; gap: 8px; margin-bottom: 8px;">
          <span style="background: ${categoryColors[pin.category]}; color: white; padding: 2px 6px; border-radius: 4px; font-size: 12px;">
            ${pin.category}
          </span>
          <span style="background: #f0f0f0; padding: 2px 6px; border-radius: 4px; font-size: 12px;">
            ${pin.priority} priority
          </span>
        </div>
        ${pin.estimatedCost ? `<p style="margin: 4px 0; font-size: 12px;"><strong>Est. Cost:</strong> $${pin.estimatedCost}</p>` : ''}
        ${pin.estimatedDuration ? `<p style="margin: 4px 0; font-size: 12px;"><strong>Duration:</strong> ${pin.estimatedDuration} min</p>` : ''}
        ${pin.notes ? `<p style="margin: 4px 0; font-size: 12px;"><strong>Notes:</strong> ${pin.notes}</p>` : ''}
        ${!readonly ? `
          <div style="margin-top: 8px; display: flex; gap: 8px;">
            <button onclick="window.editPin('${pin.id}')" style="background: #3498db; color: white; border: none; padding: 4px 8px; border-radius: 4px; font-size: 12px; cursor: pointer;">
              Edit
            </button>
            <button onclick="window.deletePin('${pin.id}')" style="background: #e74c3c; color: white; border: none; padding: 4px 8px; border-radius: 4px; font-size: 12px; cursor: pointer;">
              Delete
            </button>
          </div>
        ` : ''}
      </div>
    `
  }

  const handleMapClick = (lat: number, lng: number) => {
    if (readonly) return
    
    // Set the coordinates for the new pin
    setNewPin(prev => ({ ...prev, latitude: lat, longitude: lng }))
    setShowAddForm(true)
  }

  const addPin = () => {
    if (!newPin.name.trim()) {
      toast.error('Please enter a name for the pin')
      return
    }

    const pin: TripPin = {
      id: Date.now().toString(),
      name: newPin.name.trim(),
      description: newPin.description.trim() || undefined,
      latitude: (newPin as any).latitude,
      longitude: (newPin as any).longitude,
      category: newPin.category,
      priority: newPin.priority,
      visited: false,
      notes: newPin.notes.trim() || undefined,
      estimatedCost: newPin.estimatedCost ? parseFloat(newPin.estimatedCost) : undefined,
      estimatedDuration: newPin.estimatedDuration ? parseInt(newPin.estimatedDuration) : undefined
    }

    onPinsChange([...pins, pin])
    
    // Reset form
    setNewPin({
      name: '',
      description: '',
      category: 'attraction',
      priority: 'medium',
      notes: '',
      estimatedCost: '',
      estimatedDuration: ''
    })
    setShowAddForm(false)
    toast.success('Pin added successfully!')
  }

  const deletePin = (pinId: string) => {
    onPinsChange(pins.filter(pin => pin.id !== pinId))
    toast.success('Pin deleted successfully!')
  }

  const togglePinVisited = (pinId: string) => {
    onPinsChange(pins.map(pin => 
      pin.id === pinId ? { ...pin, visited: !pin.visited } : pin
    ))
  }

  // Expose functions to global scope for info window buttons
  useEffect(() => {
    ;(window as any).editPin = (pinId: string) => {
      const pin = pins.find(p => p.id === pinId)
      if (pin) {
        setSelectedPin(pin)
        // TODO: Implement edit functionality
        toast.info('Edit functionality coming soon!')
      }
    }

    ;(window as any).deletePin = (pinId: string) => {
      if (confirm('Are you sure you want to delete this pin?')) {
        deletePin(pinId)
      }
    }

    return () => {
      delete (window as any).editPin
      delete (window as any).deletePin
    }
  }, [pins])

  const filteredPins = pins.filter(pin =>
    pin.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    pin.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    pin.category.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const convertPinsToWaypoints = (pins: TripPin[]): RouteWaypoint[] => {
    return pins.map(pin => ({
      id: pin.id,
      name: pin.name,
      latitude: pin.latitude,
      longitude: pin.longitude,
      category: pin.category,
      priority: pin.priority,
      estimatedDuration: pin.estimatedDuration
    }))
  }

  const handleRouteOptimized = (route: OptimizedRoute) => {
    setOptimizedRoute(route)
    // Optionally update the pins order based on optimized route
    const reorderedPins = route.waypoints.map(wp =>
      pins.find(pin => pin.id === wp.id)!
    ).filter(Boolean)
    onPinsChange(reorderedPins)
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-96 bg-gray-50 rounded-lg">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading map...</p>
          <p className="text-xs text-gray-500 mt-2">
            API Key: {process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY ? 'Present' : 'Missing'}
          </p>
        </div>
      </div>
    )
  }

  // Show setup instructions if no API key
  if (!process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY) {
    return (
      <div className="space-y-4">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="flex items-center space-x-4">
            <h3 className="text-lg font-semibold text-gray-900">Trip Map</h3>
            <Badge variant="secondary">{pins.length} pins</Badge>
          </div>
        </div>

        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
          <div className="flex items-start">
            <MapIcon className="h-6 w-6 text-yellow-600 mt-1 mr-3" />
            <div>
              <h4 className="text-lg font-medium text-yellow-800 mb-2">Google Maps Setup Required</h4>
              <p className="text-yellow-700 mb-4">
                To use the interactive map feature with custom drop pins, you need to set up a Google Maps API key.
              </p>
              <div className="space-y-2 text-sm text-yellow-700">
                <p><strong>Steps to set up:</strong></p>
                <ol className="list-decimal list-inside space-y-1 ml-4">
                  <li>Go to <a href="https://console.cloud.google.com/google/maps-apis/" target="_blank" rel="noopener noreferrer" className="underline hover:text-yellow-800">Google Cloud Console</a></li>
                  <li>Create a new project or select an existing one</li>
                  <li>Enable the "Maps JavaScript API" and "Places API"</li>
                  <li>Create an API key in the "Credentials" section</li>
                  <li>Add the API key to your .env.local file as NEXT_PUBLIC_GOOGLE_MAPS_API_KEY</li>
                  <li>Restart your development server</li>
                </ol>
              </div>
              <div className="mt-4">
                <Button
                  onClick={() => window.open('https://console.cloud.google.com/google/maps-apis/', '_blank')}
                  className="bg-yellow-600 hover:bg-yellow-700 text-white"
                >
                  <NavigationIcon className="h-4 w-4 mr-2" />
                  Set Up Google Maps API
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Fallback List View */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="p-4 border-b border-gray-200">
            <h4 className="font-medium text-gray-900">Places to Visit (List View)</h4>
            <p className="text-sm text-gray-600 mt-1">Add places manually below. Map view will be available once Google Maps is configured.</p>
          </div>
          <div className="divide-y divide-gray-200">
            {pins.map((pin) => (
              <div key={pin.id} className="p-4 hover:bg-gray-50">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3">
                      <span className="text-lg">{categoryIcons[pin.category]}</span>
                      <div>
                        <h5 className={`font-medium ${pin.visited ? 'line-through text-gray-500' : 'text-gray-900'}`}>
                          {pin.name}
                        </h5>
                        {pin.description && (
                          <p className="text-sm text-gray-600 mt-1">{pin.description}</p>
                        )}
                        <div className="flex items-center space-x-2 mt-2">
                          <Badge
                            style={{ backgroundColor: categoryColors[pin.category], color: 'white' }}
                            className="text-xs"
                          >
                            {pin.category}
                          </Badge>
                          <Badge variant="secondary" className="text-xs">
                            {pin.priority} priority
                          </Badge>
                          {pin.visited && (
                            <Badge variant="secondary" className="text-xs bg-green-100 text-green-800">
                              Visited
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>

                  {!readonly && (
                    <div className="flex items-center space-x-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => togglePinVisited(pin.id)}
                        className="text-xs"
                      >
                        {pin.visited ? 'Unmark' : 'Mark Visited'}
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => deletePin(pin.id)}
                        className="text-red-600 hover:text-red-700 hover:bg-red-50"
                      >
                        <TrashIcon className="h-3 w-3" />
                      </Button>
                    </div>
                  )}
                </div>

                {(pin.estimatedCost || pin.estimatedDuration || pin.notes) && (
                  <div className="mt-3 text-sm text-gray-600 space-y-1">
                    {pin.estimatedCost && (
                      <p><strong>Est. Cost:</strong> ${pin.estimatedCost}</p>
                    )}
                    {pin.estimatedDuration && (
                      <p><strong>Duration:</strong> {pin.estimatedDuration} minutes</p>
                    )}
                    {pin.notes && (
                      <p><strong>Notes:</strong> {pin.notes}</p>
                    )}
                  </div>
                )}
              </div>
            ))}

            {pins.length === 0 && (
              <div className="p-8 text-center text-gray-500">
                <MapPinIcon className="mx-auto h-12 w-12 text-gray-300 mb-3" />
                <p className="text-sm font-medium">No places added yet</p>
                <p className="text-xs text-gray-400 mt-1">Click "Add Pin" below to add places to visit</p>
              </div>
            )}
          </div>

          {!readonly && (
            <div className="p-4 border-t border-gray-200">
              <Button
                onClick={() => {
                  // For fallback, we'll set dummy coordinates
                  setNewPin(prev => ({ ...prev, latitude: 0, longitude: 0 }))
                  setShowAddForm(true)
                }}
                className="w-full"
              >
                <PlusIcon className="h-4 w-4 mr-2" />
                Add Place to Visit
              </Button>
            </div>
          )}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* Header Controls */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="flex items-center space-x-4">
          <h3 className="text-lg font-semibold text-gray-900">Trip Map</h3>
          <Badge variant="secondary">{pins.length} pins</Badge>
        </div>
        
        <div className="flex items-center space-x-3">
          {/* Search */}
          <div className="relative">
            <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search pins..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 w-48"
            />
          </div>
          
          {/* View Mode Toggle */}
          <div className="flex items-center bg-gray-100 rounded-lg p-1">
            <Button
              variant={viewMode === 'map' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('map')}
              className="h-8"
            >
              <MapIcon className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('list')}
              className="h-8"
            >
              <ListIcon className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === 'route' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('route')}
              className="h-8"
            >
              <NavigationIcon className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Map or List View */}
      {viewMode === 'map' ? (
        <div className="relative">
          <div
            ref={mapRef}
            className="w-full h-96 rounded-lg shadow-sm border border-gray-200 bg-gray-100"
            style={{ minHeight: '384px' }}
          />
          
          {!readonly && (
            <div className="absolute top-4 right-4">
              <Button
                onClick={() => setShowAddForm(true)}
                className="bg-white shadow-lg hover:bg-gray-50 text-gray-700 border border-gray-200"
              >
                <PlusIcon className="h-4 w-4 mr-2" />
                Add Pin
              </Button>
            </div>
          )}
        </div>
      ) : (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="p-4 border-b border-gray-200">
            <h4 className="font-medium text-gray-900">Places to Visit</h4>
          </div>
          <div className="divide-y divide-gray-200">
            {filteredPins.map((pin) => (
              <div key={pin.id} className="p-4 hover:bg-gray-50">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3">
                      <span className="text-lg">{categoryIcons[pin.category]}</span>
                      <div>
                        <h5 className={`font-medium ${pin.visited ? 'line-through text-gray-500' : 'text-gray-900'}`}>
                          {pin.name}
                        </h5>
                        {pin.description && (
                          <p className="text-sm text-gray-600 mt-1">{pin.description}</p>
                        )}
                        <div className="flex items-center space-x-2 mt-2">
                          <Badge 
                            style={{ backgroundColor: categoryColors[pin.category], color: 'white' }}
                            className="text-xs"
                          >
                            {pin.category}
                          </Badge>
                          <Badge variant="secondary" className="text-xs">
                            {pin.priority} priority
                          </Badge>
                          {pin.visited && (
                            <Badge variant="secondary" className="text-xs bg-green-100 text-green-800">
                              Visited
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  {!readonly && (
                    <div className="flex items-center space-x-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => togglePinVisited(pin.id)}
                        className="text-xs"
                      >
                        {pin.visited ? 'Unmark' : 'Mark Visited'}
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => deletePin(pin.id)}
                        className="text-red-600 hover:text-red-700 hover:bg-red-50"
                      >
                        <TrashIcon className="h-3 w-3" />
                      </Button>
                    </div>
                  )}
                </div>
                
                {(pin.estimatedCost || pin.estimatedDuration || pin.notes) && (
                  <div className="mt-3 text-sm text-gray-600 space-y-1">
                    {pin.estimatedCost && (
                      <p><strong>Est. Cost:</strong> ${pin.estimatedCost}</p>
                    )}
                    {pin.estimatedDuration && (
                      <p><strong>Duration:</strong> {pin.estimatedDuration} minutes</p>
                    )}
                    {pin.notes && (
                      <p><strong>Notes:</strong> {pin.notes}</p>
                    )}
                  </div>
                )}
              </div>
            ))}
            
            {filteredPins.length === 0 && (
              <div className="p-8 text-center text-gray-500">
                {searchQuery ? 'No pins match your search.' : 'No pins added yet. Click on the map to add places to visit!'}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Add Pin Modal */}
      {showAddForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Add New Pin</h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Name *
                  </label>
                  <Input
                    value={newPin.name}
                    onChange={(e) => setNewPin(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="e.g., Eiffel Tower"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Description
                  </label>
                  <Input
                    value={newPin.description}
                    onChange={(e) => setNewPin(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="Brief description..."
                  />
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Category
                    </label>
                    <select
                      value={newPin.category}
                      onChange={(e) => setNewPin(prev => ({ ...prev, category: e.target.value as TripPin['category'] }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="attraction">Attraction</option>
                      <option value="restaurant">Restaurant</option>
                      <option value="accommodation">Accommodation</option>
                      <option value="transport">Transport</option>
                      <option value="shopping">Shopping</option>
                      <option value="other">Other</option>
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Priority
                    </label>
                    <select
                      value={newPin.priority}
                      onChange={(e) => setNewPin(prev => ({ ...prev, priority: e.target.value as TripPin['priority'] }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="high">High</option>
                      <option value="medium">Medium</option>
                      <option value="low">Low</option>
                    </select>
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Est. Cost ($)
                    </label>
                    <Input
                      type="number"
                      value={newPin.estimatedCost}
                      onChange={(e) => setNewPin(prev => ({ ...prev, estimatedCost: e.target.value }))}
                      placeholder="0"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Duration (min)
                    </label>
                    <Input
                      type="number"
                      value={newPin.estimatedDuration}
                      onChange={(e) => setNewPin(prev => ({ ...prev, estimatedDuration: e.target.value }))}
                      placeholder="60"
                    />
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Notes
                  </label>
                  <textarea
                    value={newPin.notes}
                    onChange={(e) => setNewPin(prev => ({ ...prev, notes: e.target.value }))}
                    placeholder="Additional notes..."
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>
              
              <div className="flex justify-end space-x-3 mt-6">
                <Button
                  variant="outline"
                  onClick={() => setShowAddForm(false)}
                >
                  Cancel
                </Button>
                <Button onClick={addPin}>
                  Add Pin
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
