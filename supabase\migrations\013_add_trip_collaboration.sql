-- Add collaboration features to trips table
ALTER TABLE public.trips 
ADD COLUMN IF NOT EXISTS is_shared BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS share_code TEXT UNIQUE,
ADD COLUMN IF NOT EXISTS share_permissions TEXT CHECK (share_permissions IN ('view', 'edit', 'admin')) DEFAULT 'view';

-- Create trip_collaborators table
CREATE TABLE IF NOT EXISTS public.trip_collaborators (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  trip_id UUID REFERENCES public.trips(id) ON DELETE CASCADE NOT NULL,
  user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
  invited_by UUID REFERENCES public.profiles(id) ON DELETE SET NULL,
  permission_level TEXT CHECK (permission_level IN ('view', 'edit', 'admin')) DEFAULT 'view',
  status TEXT CHECK (status IN ('pending', 'accepted', 'declined')) DEFAULT 'pending',
  invited_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  responded_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(trip_id, user_id)
);

-- Create trip_invitations table for email-based invitations
CREATE TABLE IF NOT EXISTS public.trip_invitations (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  trip_id UUID REFERENCES public.trips(id) ON DELETE CASCADE NOT NULL,
  invited_by UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
  email TEXT NOT NULL,
  permission_level TEXT CHECK (permission_level IN ('view', 'edit', 'admin')) DEFAULT 'view',
  invitation_token TEXT UNIQUE NOT NULL,
  status TEXT CHECK (status IN ('pending', 'accepted', 'declined', 'expired')) DEFAULT 'pending',
  expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '7 days'),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create trip_activity_log table for tracking changes
CREATE TABLE IF NOT EXISTS public.trip_activity_log (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  trip_id UUID REFERENCES public.trips(id) ON DELETE CASCADE NOT NULL,
  user_id UUID REFERENCES public.profiles(id) ON DELETE SET NULL,
  action_type TEXT NOT NULL, -- 'created', 'updated', 'deleted', 'shared', 'joined', 'left', 'pin_added', 'pin_updated', 'pin_deleted'
  entity_type TEXT, -- 'trip', 'pin', 'expense', 'activity'
  entity_id UUID,
  description TEXT,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add RLS policies for trip_collaborators
ALTER TABLE public.trip_collaborators ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view collaborators for trips they have access to" ON public.trip_collaborators
  FOR SELECT USING (
    trip_id IN (
      SELECT id FROM public.trips WHERE user_id = auth.uid()
      UNION
      SELECT trip_id FROM public.trip_collaborators WHERE user_id = auth.uid() AND status = 'accepted'
    )
  );

CREATE POLICY "Trip owners can manage collaborators" ON public.trip_collaborators
  FOR ALL USING (
    trip_id IN (SELECT id FROM public.trips WHERE user_id = auth.uid())
  );

CREATE POLICY "Users can update their own collaboration status" ON public.trip_collaborators
  FOR UPDATE USING (user_id = auth.uid());

-- Add RLS policies for trip_invitations
ALTER TABLE public.trip_invitations ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view invitations for their trips" ON public.trip_invitations
  FOR SELECT USING (
    trip_id IN (SELECT id FROM public.trips WHERE user_id = auth.uid())
  );

CREATE POLICY "Trip owners can manage invitations" ON public.trip_invitations
  FOR ALL USING (
    trip_id IN (SELECT id FROM public.trips WHERE user_id = auth.uid())
  );

-- Add RLS policies for trip_activity_log
ALTER TABLE public.trip_activity_log ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view activity for trips they have access to" ON public.trip_activity_log
  FOR SELECT USING (
    trip_id IN (
      SELECT id FROM public.trips WHERE user_id = auth.uid()
      UNION
      SELECT trip_id FROM public.trip_collaborators WHERE user_id = auth.uid() AND status = 'accepted'
    )
  );

CREATE POLICY "Users can create activity logs for trips they have access to" ON public.trip_activity_log
  FOR INSERT WITH CHECK (
    trip_id IN (
      SELECT id FROM public.trips WHERE user_id = auth.uid()
      UNION
      SELECT trip_id FROM public.trip_collaborators WHERE user_id = auth.uid() AND status = 'accepted'
    )
  );

-- Update trips RLS policy to include shared trips
DROP POLICY IF EXISTS "Users can view their own trips" ON public.trips;
CREATE POLICY "Users can view their own trips and shared trips" ON public.trips
  FOR SELECT USING (
    user_id = auth.uid() 
    OR id IN (
      SELECT trip_id FROM public.trip_collaborators 
      WHERE user_id = auth.uid() AND status = 'accepted'
    )
  );

-- Function to generate share codes
CREATE OR REPLACE FUNCTION generate_trip_share_code()
RETURNS TEXT AS $$
BEGIN
  RETURN upper(substring(md5(random()::text) from 1 for 8));
END;
$$ LANGUAGE plpgsql;

-- Function to log trip activities
CREATE OR REPLACE FUNCTION log_trip_activity(
  p_trip_id UUID,
  p_user_id UUID,
  p_action_type TEXT,
  p_entity_type TEXT DEFAULT NULL,
  p_entity_id UUID DEFAULT NULL,
  p_description TEXT DEFAULT NULL,
  p_metadata JSONB DEFAULT '{}'
)
RETURNS UUID AS $$
DECLARE
  activity_id UUID;
BEGIN
  INSERT INTO public.trip_activity_log (
    trip_id, user_id, action_type, entity_type, entity_id, description, metadata
  ) VALUES (
    p_trip_id, p_user_id, p_action_type, p_entity_type, p_entity_id, p_description, p_metadata
  ) RETURNING id INTO activity_id;
  
  RETURN activity_id;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically generate share codes when trips are shared
CREATE OR REPLACE FUNCTION auto_generate_share_code()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.is_shared = TRUE AND (OLD.is_shared = FALSE OR OLD.share_code IS NULL) THEN
    NEW.share_code = generate_trip_share_code();
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_auto_generate_share_code
  BEFORE UPDATE ON public.trips
  FOR EACH ROW
  EXECUTE FUNCTION auto_generate_share_code();

-- Trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_trip_collaborators_updated_at
  BEFORE UPDATE ON public.trip_collaborators
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_trip_invitations_updated_at
  BEFORE UPDATE ON public.trip_invitations
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_trip_collaborators_trip_id ON public.trip_collaborators(trip_id);
CREATE INDEX IF NOT EXISTS idx_trip_collaborators_user_id ON public.trip_collaborators(user_id);
CREATE INDEX IF NOT EXISTS idx_trip_collaborators_status ON public.trip_collaborators(status);
CREATE INDEX IF NOT EXISTS idx_trip_invitations_trip_id ON public.trip_invitations(trip_id);
CREATE INDEX IF NOT EXISTS idx_trip_invitations_email ON public.trip_invitations(email);
CREATE INDEX IF NOT EXISTS idx_trip_invitations_token ON public.trip_invitations(invitation_token);
CREATE INDEX IF NOT EXISTS idx_trip_activity_log_trip_id ON public.trip_activity_log(trip_id);
CREATE INDEX IF NOT EXISTS idx_trip_activity_log_user_id ON public.trip_activity_log(user_id);
CREATE INDEX IF NOT EXISTS idx_trips_share_code ON public.trips(share_code);

-- Add comments
COMMENT ON TABLE public.trip_collaborators IS 'Users who have been invited to collaborate on trips';
COMMENT ON TABLE public.trip_invitations IS 'Email-based invitations for trip collaboration';
COMMENT ON TABLE public.trip_activity_log IS 'Log of all activities and changes made to trips';
COMMENT ON COLUMN public.trips.share_code IS 'Unique code for sharing trips via link';
COMMENT ON COLUMN public.trips.share_permissions IS 'Default permission level for shared access';
COMMENT ON COLUMN public.trip_collaborators.permission_level IS 'Permission level: view, edit, or admin';
COMMENT ON COLUMN public.trip_collaborators.status IS 'Invitation status: pending, accepted, or declined';
