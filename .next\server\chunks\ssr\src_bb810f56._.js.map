{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/lib/services/trip-planner-service.ts"], "sourcesContent": ["export interface Trip {\n  id: string\n  user_id: string\n  title: string\n  description?: string\n  destination_country: string\n  destination_city?: string\n  start_date: string\n  end_date: string\n  trip_type: 'leisure' | 'business' | 'adventure' | 'family' | 'romantic' | 'solo' | 'group'\n  status: 'planning' | 'booked' | 'in_progress' | 'completed' | 'cancelled'\n  budget_total?: number\n  budget_spent: number\n  currency: string\n  traveler_count: number\n  is_shared: boolean\n  cover_image_url?: string\n  notes?: string\n  created_at: string\n  updated_at: string\n  trip_destinations?: TripDestination[]\n}\n\nexport interface TripDestination {\n  id: string\n  trip_id: string\n  name: string\n  country: string\n  city?: string\n  latitude?: number\n  longitude?: number\n  arrival_date?: string\n  departure_date?: string\n  order_index: number\n  accommodation_name?: string\n  accommodation_address?: string\n  accommodation_cost?: number\n  notes?: string\n  created_at: string\n  updated_at: string\n  trip_activities?: TripActivity[]\n}\n\nexport interface TripActivity {\n  id: string\n  trip_id: string\n  destination_id?: string\n  title: string\n  description?: string\n  category: 'sightseeing' | 'dining' | 'entertainment' | 'shopping' | 'transport' | 'accommodation' | 'adventure' | 'cultural' | 'relaxation' | 'business'\n  scheduled_date?: string\n  scheduled_time?: string\n  duration_minutes?: number\n  cost?: number\n  currency: string\n  location_name?: string\n  location_address?: string\n  latitude?: number\n  longitude?: number\n  booking_reference?: string\n  booking_url?: string\n  status: 'planned' | 'booked' | 'completed' | 'cancelled'\n  priority: 'low' | 'medium' | 'high' | 'must_do'\n  notes?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface TripExpense {\n  id: string\n  trip_id: string\n  destination_id?: string\n  activity_id?: string\n  category: 'accommodation' | 'transport' | 'food' | 'activities' | 'shopping' | 'fuel' | 'parking' | 'tolls' | 'insurance' | 'visas' | 'other'\n  subcategory?: string\n  description: string\n  amount: number\n  currency: string\n  exchange_rate: number\n  amount_usd: number\n  expense_date: string\n  payment_method?: string\n  receipt_url?: string\n  is_shared_expense: boolean\n  shared_with_count: number\n  notes?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface TripTransport {\n  id: string\n  trip_id: string\n  from_destination_id?: string\n  to_destination_id?: string\n  transport_type: 'flight' | 'train' | 'bus' | 'car_rental' | 'taxi' | 'rideshare' | 'ferry' | 'walking' | 'cycling' | 'other'\n  provider_name?: string\n  departure_datetime?: string\n  arrival_datetime?: string\n  duration_minutes?: number\n  distance_km?: number\n  cost?: number\n  currency: string\n  booking_reference?: string\n  booking_url?: string\n  from_location?: string\n  to_location?: string\n  from_latitude?: number\n  from_longitude?: number\n  to_latitude?: number\n  to_longitude?: number\n  fuel_cost?: number\n  fuel_consumption_liters?: number\n  toll_costs?: number\n  parking_costs?: number\n  notes?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface CreateTripData {\n  title: string\n  description?: string\n  destination_country: string\n  destination_city?: string\n  start_date: string\n  end_date: string\n  trip_type?: 'leisure' | 'business' | 'adventure' | 'family' | 'romantic' | 'solo' | 'group'\n  budget_total?: number\n  currency?: string\n  traveler_count?: number\n  cover_image_url?: string\n  notes?: string\n}\n\nexport interface UpdateTripData extends Partial<CreateTripData> {}\n\nexport interface CreateDestinationData {\n  name: string\n  country: string\n  city?: string\n  latitude?: number\n  longitude?: number\n  arrival_date?: string\n  departure_date?: string\n  order_index?: number\n  accommodation_name?: string\n  accommodation_address?: string\n  accommodation_cost?: number\n  notes?: string\n}\n\nexport interface CreateActivityData {\n  destination_id?: string\n  title: string\n  description?: string\n  category: 'sightseeing' | 'dining' | 'entertainment' | 'shopping' | 'transport' | 'accommodation' | 'adventure' | 'cultural' | 'relaxation' | 'business'\n  scheduled_date?: string\n  scheduled_time?: string\n  duration_minutes?: number\n  cost?: number\n  currency?: string\n  location_name?: string\n  location_address?: string\n  latitude?: number\n  longitude?: number\n  booking_reference?: string\n  booking_url?: string\n  priority?: 'low' | 'medium' | 'high' | 'must_do'\n  notes?: string\n}\n\nexport interface CreateExpenseData {\n  destination_id?: string\n  activity_id?: string\n  category: 'accommodation' | 'transport' | 'food' | 'activities' | 'shopping' | 'fuel' | 'parking' | 'tolls' | 'insurance' | 'visas' | 'other'\n  subcategory?: string\n  description: string\n  amount: number\n  currency?: string\n  exchange_rate?: number\n  expense_date: string\n  payment_method?: string\n  receipt_url?: string\n  is_shared_expense?: boolean\n  shared_with_count?: number\n  notes?: string\n}\n\nclass TripPlannerService {\n  private baseUrl = '/api/trips'\n\n  // Trip CRUD operations\n  async getTrips(): Promise<Trip[]> {\n    const response = await fetch(this.baseUrl, {\n      method: 'GET',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    })\n\n    if (!response.ok) {\n      throw new Error(`Failed to fetch trips: ${response.statusText}`)\n    }\n\n    const data = await response.json()\n    return data.trips || []\n  }\n\n  async getTrip(id: string): Promise<Trip> {\n    const response = await fetch(`${this.baseUrl}/${id}`, {\n      method: 'GET',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    })\n\n    if (!response.ok) {\n      throw new Error(`Failed to fetch trip: ${response.statusText}`)\n    }\n\n    const data = await response.json()\n    return data.trip\n  }\n\n  async createTrip(data: CreateTripData): Promise<Trip> {\n    const response = await fetch(this.baseUrl, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(data),\n    })\n\n    if (!response.ok) {\n      throw new Error(`Failed to create trip: ${response.statusText}`)\n    }\n\n    const result = await response.json()\n    return result.trip\n  }\n\n  async updateTrip(id: string, data: UpdateTripData): Promise<Trip> {\n    const response = await fetch(`${this.baseUrl}/${id}`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(data),\n    })\n\n    if (!response.ok) {\n      throw new Error(`Failed to update trip: ${response.statusText}`)\n    }\n\n    const result = await response.json()\n    return result.trip\n  }\n\n  async deleteTrip(id: string): Promise<void> {\n    const response = await fetch(`${this.baseUrl}/${id}`, {\n      method: 'DELETE',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    })\n\n    if (!response.ok) {\n      throw new Error(`Failed to delete trip: ${response.statusText}`)\n    }\n  }\n\n  // Destination operations\n  async getDestinations(tripId: string): Promise<TripDestination[]> {\n    const response = await fetch(`${this.baseUrl}/${tripId}/destinations`, {\n      method: 'GET',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    })\n\n    if (!response.ok) {\n      throw new Error(`Failed to fetch destinations: ${response.statusText}`)\n    }\n\n    const data = await response.json()\n    return data.destinations || []\n  }\n\n  async addDestination(tripId: string, data: CreateDestinationData): Promise<TripDestination> {\n    const response = await fetch(`${this.baseUrl}/${tripId}/destinations`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(data),\n    })\n\n    if (!response.ok) {\n      throw new Error(`Failed to add destination: ${response.statusText}`)\n    }\n\n    const result = await response.json()\n    return result.destination\n  }\n\n  // Activity operations\n  async getActivities(tripId: string): Promise<TripActivity[]> {\n    const response = await fetch(`${this.baseUrl}/${tripId}/activities`, {\n      method: 'GET',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    })\n\n    if (!response.ok) {\n      throw new Error(`Failed to fetch activities: ${response.statusText}`)\n    }\n\n    const data = await response.json()\n    return data.activities || []\n  }\n\n  async addActivity(tripId: string, data: CreateActivityData): Promise<TripActivity> {\n    const response = await fetch(`${this.baseUrl}/${tripId}/activities`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(data),\n    })\n\n    if (!response.ok) {\n      throw new Error(`Failed to add activity: ${response.statusText}`)\n    }\n\n    const result = await response.json()\n    return result.activity\n  }\n\n  // Expense operations\n  async getExpenses(tripId: string): Promise<TripExpense[]> {\n    const response = await fetch(`${this.baseUrl}/${tripId}/expenses`, {\n      method: 'GET',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    })\n\n    if (!response.ok) {\n      throw new Error(`Failed to fetch expenses: ${response.statusText}`)\n    }\n\n    const data = await response.json()\n    return data.expenses || []\n  }\n\n  async addExpense(tripId: string, data: CreateExpenseData): Promise<TripExpense> {\n    const response = await fetch(`${this.baseUrl}/${tripId}/expenses`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(data),\n    })\n\n    if (!response.ok) {\n      throw new Error(`Failed to add expense: ${response.statusText}`)\n    }\n\n    const result = await response.json()\n    return result.expense\n  }\n\n  // Utility methods\n  calculateTripDuration(startDate: string, endDate: string): number {\n    const start = new Date(startDate)\n    const end = new Date(endDate)\n    const diffTime = Math.abs(end.getTime() - start.getTime())\n    return Math.ceil(diffTime / (1000 * 60 * 60 * 24))\n  }\n\n  calculateBudgetProgress(budgetTotal?: number, budgetSpent?: number): number {\n    if (!budgetTotal || budgetTotal === 0) return 0\n    return Math.min((budgetSpent || 0) / budgetTotal * 100, 100)\n  }\n\n  formatCurrency(amount: number, currency: string = 'USD'): string {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: currency,\n    }).format(amount)\n  }\n}\n\nexport const tripPlannerService = new TripPlannerService()\n"], "names": [], "mappings": ";;;AA6LA,MAAM;IACI,UAAU,aAAY;IAE9B,uBAAuB;IACvB,MAAM,WAA4B;QAChC,MAAM,WAAW,MAAM,MAAM,IAAI,CAAC,OAAO,EAAE;YACzC,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,uBAAuB,EAAE,SAAS,UAAU,EAAE;QACjE;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO,KAAK,KAAK,IAAI,EAAE;IACzB;IAEA,MAAM,QAAQ,EAAU,EAAiB;QACvC,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE;YACpD,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,sBAAsB,EAAE,SAAS,UAAU,EAAE;QAChE;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO,KAAK,IAAI;IAClB;IAEA,MAAM,WAAW,IAAoB,EAAiB;QACpD,MAAM,WAAW,MAAM,MAAM,IAAI,CAAC,OAAO,EAAE;YACzC,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,uBAAuB,EAAE,SAAS,UAAU,EAAE;QACjE;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAClC,OAAO,OAAO,IAAI;IACpB;IAEA,MAAM,WAAW,EAAU,EAAE,IAAoB,EAAiB;QAChE,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE;YACpD,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,uBAAuB,EAAE,SAAS,UAAU,EAAE;QACjE;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAClC,OAAO,OAAO,IAAI;IACpB;IAEA,MAAM,WAAW,EAAU,EAAiB;QAC1C,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE;YACpD,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,uBAAuB,EAAE,SAAS,UAAU,EAAE;QACjE;IACF;IAEA,yBAAyB;IACzB,MAAM,gBAAgB,MAAc,EAA8B;QAChE,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,OAAO,aAAa,CAAC,EAAE;YACrE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,8BAA8B,EAAE,SAAS,UAAU,EAAE;QACxE;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO,KAAK,YAAY,IAAI,EAAE;IAChC;IAEA,MAAM,eAAe,MAAc,EAAE,IAA2B,EAA4B;QAC1F,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,OAAO,aAAa,CAAC,EAAE;YACrE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,2BAA2B,EAAE,SAAS,UAAU,EAAE;QACrE;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAClC,OAAO,OAAO,WAAW;IAC3B;IAEA,sBAAsB;IACtB,MAAM,cAAc,MAAc,EAA2B;QAC3D,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,OAAO,WAAW,CAAC,EAAE;YACnE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,4BAA4B,EAAE,SAAS,UAAU,EAAE;QACtE;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO,KAAK,UAAU,IAAI,EAAE;IAC9B;IAEA,MAAM,YAAY,MAAc,EAAE,IAAwB,EAAyB;QACjF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,OAAO,WAAW,CAAC,EAAE;YACnE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,SAAS,UAAU,EAAE;QAClE;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAClC,OAAO,OAAO,QAAQ;IACxB;IAEA,qBAAqB;IACrB,MAAM,YAAY,MAAc,EAA0B;QACxD,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,OAAO,SAAS,CAAC,EAAE;YACjE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,0BAA0B,EAAE,SAAS,UAAU,EAAE;QACpE;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO,KAAK,QAAQ,IAAI,EAAE;IAC5B;IAEA,MAAM,WAAW,MAAc,EAAE,IAAuB,EAAwB;QAC9E,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,OAAO,SAAS,CAAC,EAAE;YACjE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,uBAAuB,EAAE,SAAS,UAAU,EAAE;QACjE;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAClC,OAAO,OAAO,OAAO;IACvB;IAEA,kBAAkB;IAClB,sBAAsB,SAAiB,EAAE,OAAe,EAAU;QAChE,MAAM,QAAQ,IAAI,KAAK;QACvB,MAAM,MAAM,IAAI,KAAK;QACrB,MAAM,WAAW,KAAK,GAAG,CAAC,IAAI,OAAO,KAAK,MAAM,OAAO;QACvD,OAAO,KAAK,IAAI,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;IAClD;IAEA,wBAAwB,WAAoB,EAAE,WAAoB,EAAU;QAC1E,IAAI,CAAC,eAAe,gBAAgB,GAAG,OAAO;QAC9C,OAAO,KAAK,GAAG,CAAC,CAAC,eAAe,CAAC,IAAI,cAAc,KAAK;IAC1D;IAEA,eAAe,MAAc,EAAE,WAAmB,KAAK,EAAU;QAC/D,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;QACZ,GAAG,MAAM,CAAC;IACZ;AACF;AAEO,MAAM,qBAAqB,IAAI", "debugId": null}}, {"offset": {"line": 187, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 203, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/components/ui/Button.tsx"], "sourcesContent": ["import { ButtonHTMLAttributes, forwardRef } from 'react'\nimport { cn } from '@/lib/utils'\nimport { cva, type VariantProps } from 'class-variance-authority'\n\nconst buttonVariants = cva(\n  'inline-flex items-center justify-center rounded-lg text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none disabled:cursor-not-allowed ring-offset-background transform hover:scale-105 active:scale-95 focus:scale-105',\n  {\n    variants: {\n      variant: {\n        default: 'bg-gradient-to-r from-blue-600 to-blue-700 text-white hover:from-blue-700 hover:to-blue-800 shadow-lg hover:shadow-xl focus-visible:ring-blue-500',\n        destructive: 'bg-gradient-to-r from-red-600 to-red-700 text-white hover:from-red-700 hover:to-red-800 shadow-lg hover:shadow-xl focus-visible:ring-red-500',\n        outline: 'border-2 border-gray-300 bg-white text-gray-700 hover:bg-gray-50 hover:border-gray-400 focus-visible:ring-gray-500',\n        secondary: 'bg-gradient-to-r from-gray-100 to-gray-200 text-gray-900 hover:from-gray-200 hover:to-gray-300 focus-visible:ring-gray-500',\n        ghost: 'hover:bg-gray-100 text-gray-700 focus-visible:ring-gray-500',\n        link: 'underline-offset-4 hover:underline text-blue-600 focus-visible:ring-blue-500',\n        success: 'bg-gradient-to-r from-green-600 to-green-700 text-white hover:from-green-700 hover:to-green-800 shadow-lg hover:shadow-xl focus-visible:ring-green-500',\n        warning: 'bg-gradient-to-r from-yellow-500 to-yellow-600 text-white hover:from-yellow-600 hover:to-yellow-700 shadow-lg hover:shadow-xl focus-visible:ring-yellow-500',\n      },\n      size: {\n        default: 'h-10 py-2 px-4',\n        sm: 'h-9 px-3 text-xs',\n        lg: 'h-12 px-8 text-base',\n        xl: 'h-14 px-10 text-lg',\n        icon: 'h-10 w-10',\n      },\n    },\n    defaultVariants: {\n      variant: 'default',\n      size: 'default',\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  loading?: boolean\n  loadingText?: string\n}\n\nconst Button = forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, loading, loadingText, children, disabled, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        disabled={disabled || loading}\n        aria-disabled={disabled || loading}\n        {...props}\n      >\n        {loading ? (\n          <>\n            <svg\n              className=\"animate-spin -ml-1 mr-2 h-4 w-4\"\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              aria-hidden=\"true\"\n            >\n              <circle\n                className=\"opacity-25\"\n                cx=\"12\"\n                cy=\"12\"\n                r=\"10\"\n                stroke=\"currentColor\"\n                strokeWidth=\"4\"\n              ></circle>\n              <path\n                className=\"opacity-75\"\n                fill=\"currentColor\"\n                d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n              ></path>\n            </svg>\n            {loadingText || 'Loading...'}\n          </>\n        ) : (\n          children\n        )}\n      </button>\n    )\n  }\n)\nButton.displayName = 'Button'\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,gVACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aAAa;YACb,SAAS;YACT,WAAW;YACX,OAAO;YACP,MAAM;YACN,SAAS;YACT,SAAS;QACX;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAUF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACtB,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACjF,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACL,UAAU,YAAY;QACtB,iBAAe,YAAY;QAC1B,GAAG,KAAK;kBAER,wBACC;;8BACE,8OAAC;oBACC,WAAU;oBACV,OAAM;oBACN,MAAK;oBACL,SAAQ;oBACR,eAAY;;sCAEZ,8OAAC;4BACC,WAAU;4BACV,IAAG;4BACH,IAAG;4BACH,GAAE;4BACF,QAAO;4BACP,aAAY;;;;;;sCAEd,8OAAC;4BACC,WAAU;4BACV,MAAK;4BACL,GAAE;;;;;;;;;;;;gBAGL,eAAe;;2BAGlB;;;;;;AAIR;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 304, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/components/ui/Loading.tsx"], "sourcesContent": ["import { cn } from '@/lib/utils'\n\ninterface LoadingProps {\n  size?: 'sm' | 'md' | 'lg' | 'xl'\n  variant?: 'spinner' | 'dots' | 'pulse'\n  className?: string\n  text?: string\n}\n\nconst sizeClasses = {\n  sm: 'h-4 w-4',\n  md: 'h-6 w-6',\n  lg: 'h-8 w-8',\n  xl: 'h-12 w-12',\n}\n\nexport function Loading({ \n  size = 'md', \n  variant = 'spinner', \n  className,\n  text \n}: LoadingProps) {\n  if (variant === 'spinner') {\n    return (\n      <div className={cn('flex flex-col items-center justify-center space-y-2', className)}>\n        <div\n          className={cn(\n            'animate-spin rounded-full border-2 border-gray-300 border-t-blue-600',\n            sizeClasses[size]\n          )}\n        />\n        {text && <p className=\"text-sm text-gray-600\">{text}</p>}\n      </div>\n    )\n  }\n\n  if (variant === 'dots') {\n    return (\n      <div className={cn('flex flex-col items-center justify-center space-y-2', className)}>\n        <div className=\"flex space-x-1\">\n          <div className=\"h-2 w-2 bg-blue-600 rounded-full animate-bounce [animation-delay:-0.3s]\"></div>\n          <div className=\"h-2 w-2 bg-blue-600 rounded-full animate-bounce [animation-delay:-0.15s]\"></div>\n          <div className=\"h-2 w-2 bg-blue-600 rounded-full animate-bounce\"></div>\n        </div>\n        {text && <p className=\"text-sm text-gray-600\">{text}</p>}\n      </div>\n    )\n  }\n\n  if (variant === 'pulse') {\n    return (\n      <div className={cn('flex flex-col items-center justify-center space-y-2', className)}>\n        <div\n          className={cn(\n            'bg-blue-600 rounded-full animate-pulse',\n            sizeClasses[size]\n          )}\n        />\n        {text && <p className=\"text-sm text-gray-600\">{text}</p>}\n      </div>\n    )\n  }\n\n  return null\n}\n\nexport function LoadingSkeleton({ className }: { className?: string }) {\n  return (\n    <div className={cn('animate-pulse', className)}>\n      <div className=\"bg-gray-200 rounded-lg h-4 w-full\"></div>\n    </div>\n  )\n}\n\nexport function LoadingCard() {\n  return (\n    <div className=\"bg-white rounded-xl border border-gray-200 shadow-sm p-6 animate-pulse\">\n      <div className=\"space-y-4\">\n        <div className=\"h-4 bg-gray-200 rounded w-3/4\"></div>\n        <div className=\"space-y-2\">\n          <div className=\"h-3 bg-gray-200 rounded\"></div>\n          <div className=\"h-3 bg-gray-200 rounded w-5/6\"></div>\n        </div>\n        <div className=\"h-8 bg-gray-200 rounded w-1/4\"></div>\n      </div>\n    </div>\n  )\n}\n\nexport function LoadingTable({ rows = 5 }: { rows?: number }) {\n  return (\n    <div className=\"bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden\">\n      <div className=\"p-6 border-b border-gray-200\">\n        <div className=\"h-6 bg-gray-200 rounded w-1/3 animate-pulse\"></div>\n      </div>\n      <div className=\"divide-y divide-gray-200\">\n        {Array.from({ length: rows }).map((_, i) => (\n          <div key={i} className=\"p-4 animate-pulse\">\n            <div className=\"flex items-center space-x-4\">\n              <div className=\"h-4 w-4 bg-gray-200 rounded\"></div>\n              <div className=\"flex-1 space-y-2\">\n                <div className=\"h-4 bg-gray-200 rounded w-3/4\"></div>\n                <div className=\"h-3 bg-gray-200 rounded w-1/2\"></div>\n              </div>\n              <div className=\"h-8 w-8 bg-gray-200 rounded\"></div>\n            </div>\n          </div>\n        ))}\n      </div>\n    </div>\n  )\n}\n\nexport function LoadingStats() {\n  return (\n    <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n      {Array.from({ length: 4 }).map((_, i) => (\n        <div key={i} className=\"bg-white rounded-xl border border-gray-200 shadow-sm p-4 animate-pulse\">\n          <div className=\"text-center space-y-2\">\n            <div className=\"h-8 bg-gray-200 rounded w-16 mx-auto\"></div>\n            <div className=\"h-4 bg-gray-200 rounded w-20 mx-auto\"></div>\n          </div>\n        </div>\n      ))}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;;;AASA,MAAM,cAAc;IAClB,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;AACN;AAEO,SAAS,QAAQ,EACtB,OAAO,IAAI,EACX,UAAU,SAAS,EACnB,SAAS,EACT,IAAI,EACS;IACb,IAAI,YAAY,WAAW;QACzB,qBACE,8OAAC;YAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uDAAuD;;8BACxE,8OAAC;oBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wEACA,WAAW,CAAC,KAAK;;;;;;gBAGpB,sBAAQ,8OAAC;oBAAE,WAAU;8BAAyB;;;;;;;;;;;;IAGrD;IAEA,IAAI,YAAY,QAAQ;QACtB,qBACE,8OAAC;YAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uDAAuD;;8BACxE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;;;;;;;;;;;;gBAEhB,sBAAQ,8OAAC;oBAAE,WAAU;8BAAyB;;;;;;;;;;;;IAGrD;IAEA,IAAI,YAAY,SAAS;QACvB,qBACE,8OAAC;YAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uDAAuD;;8BACxE,8OAAC;oBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0CACA,WAAW,CAAC,KAAK;;;;;;gBAGpB,sBAAQ,8OAAC;oBAAE,WAAU;8BAAyB;;;;;;;;;;;;IAGrD;IAEA,OAAO;AACT;AAEO,SAAS,gBAAgB,EAAE,SAAS,EAA0B;IACnE,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;kBAClC,cAAA,8OAAC;YAAI,WAAU;;;;;;;;;;;AAGrB;AAEO,SAAS;IACd,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;;;;;8BACf,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;;;;;;;;;;;;8BAEjB,8OAAC;oBAAI,WAAU;;;;;;;;;;;;;;;;;AAIvB;AAEO,SAAS,aAAa,EAAE,OAAO,CAAC,EAAqB;IAC1D,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;;;;;;;;;;0BAEjB,8OAAC;gBAAI,WAAU;0BACZ,MAAM,IAAI,CAAC;oBAAE,QAAQ;gBAAK,GAAG,GAAG,CAAC,CAAC,GAAG,kBACpC,8OAAC;wBAAY,WAAU;kCACrB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;;;;;;;;;;;8CAEjB,8OAAC;oCAAI,WAAU;;;;;;;;;;;;uBAPT;;;;;;;;;;;;;;;;AAcpB;AAEO,SAAS;IACd,qBACE,8OAAC;QAAI,WAAU;kBACZ,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBACjC,8OAAC;gBAAY,WAAU;0BACrB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;;;;;;;;;;;;eAHT;;;;;;;;;;AASlB", "debugId": null}}, {"offset": {"line": 630, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/components/ui/Badge.tsx"], "sourcesContent": ["import { HTMLAttributes, forwardRef } from 'react'\nimport { cva, type VariantProps } from 'class-variance-authority'\nimport { cn } from '@/lib/utils'\n\nconst badgeVariants = cva(\n  'inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium transition-all duration-200',\n  {\n    variants: {\n      variant: {\n        default: 'bg-blue-100 text-blue-800 hover:bg-blue-200',\n        secondary: 'bg-gray-100 text-gray-800 hover:bg-gray-200',\n        destructive: 'bg-red-100 text-red-800 hover:bg-red-200',\n        success: 'bg-green-100 text-green-800 hover:bg-green-200',\n        warning: 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200',\n        outline: 'border border-gray-300 text-gray-700 hover:bg-gray-50',\n        purple: 'bg-purple-100 text-purple-800 hover:bg-purple-200',\n        indigo: 'bg-indigo-100 text-indigo-800 hover:bg-indigo-200',\n        pink: 'bg-pink-100 text-pink-800 hover:bg-pink-200',\n        orange: 'bg-orange-100 text-orange-800 hover:bg-orange-200',\n      },\n      size: {\n        default: 'px-2.5 py-0.5 text-xs',\n        sm: 'px-2 py-0.5 text-xs',\n        lg: 'px-3 py-1 text-sm',\n      },\n    },\n    defaultVariants: {\n      variant: 'default',\n      size: 'default',\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nconst Badge = forwardRef<HTMLDivElement, BadgeProps>(\n  ({ className, variant, size, ...props }, ref) => {\n    return (\n      <div\n        ref={ref}\n        className={cn(badgeVariants({ variant, size }), className)}\n        {...props}\n      />\n    )\n  }\n)\nBadge.displayName = 'Badge'\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,uGACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,WAAW;YACX,aAAa;YACb,SAAS;YACT,SAAS;YACT,SAAS;YACT,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,QAAQ;QACV;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAOF,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACrB,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IACvC,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;YAAS;QAAK,IAAI;QAC/C,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 689, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/components/ui/Input.tsx"], "sourcesContent": ["import { InputHTMLAttributes, forwardRef } from 'react'\nimport { cn } from '@/lib/utils'\n\nexport interface InputProps extends InputHTMLAttributes<HTMLInputElement> {\n  error?: boolean\n  helperText?: string\n  label?: string\n}\n\nconst Input = forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, error, helperText, label, id, ...props }, ref) => {\n    const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`\n    const helperTextId = helperText ? `${inputId}-helper` : undefined\n\n    return (\n      <div className=\"w-full\">\n        {label && (\n          <label\n            htmlFor={inputId}\n            className=\"block text-sm font-medium text-gray-700 mb-1\"\n          >\n            {label}\n            {props.required && <span className=\"text-red-500 ml-1\" aria-label=\"required\">*</span>}\n          </label>\n        )}\n        <input\n          id={inputId}\n          type={type}\n          className={cn(\n            'flex h-10 w-full rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm ring-offset-white file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 transition-all duration-200 hover:border-gray-400',\n            error && 'border-red-500 focus-visible:ring-red-500 aria-invalid:border-red-500',\n            className\n          )}\n          ref={ref}\n          aria-invalid={error}\n          aria-describedby={helperTextId}\n          {...props}\n        />\n        {helperText && (\n          <p\n            id={helperTextId}\n            className={cn(\n              'mt-1 text-xs',\n              error ? 'text-red-600' : 'text-gray-500'\n            )}\n            role={error ? 'alert' : 'status'}\n          >\n            {helperText}\n          </p>\n        )}\n      </div>\n    )\n  }\n)\nInput.displayName = 'Input'\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAQA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACrB,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE,EAAE,EAAE,GAAG,OAAO,EAAE;IAC5D,MAAM,UAAU,MAAM,CAAC,MAAM,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;IACxE,MAAM,eAAe,aAAa,GAAG,QAAQ,OAAO,CAAC,GAAG;IAExD,qBACE,8OAAC;QAAI,WAAU;;YACZ,uBACC,8OAAC;gBACC,SAAS;gBACT,WAAU;;oBAET;oBACA,MAAM,QAAQ,kBAAI,8OAAC;wBAAK,WAAU;wBAAoB,cAAW;kCAAW;;;;;;;;;;;;0BAGjF,8OAAC;gBACC,IAAI;gBACJ,MAAM;gBACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uYACA,SAAS,yEACT;gBAEF,KAAK;gBACL,gBAAc;gBACd,oBAAkB;gBACjB,GAAG,KAAK;;;;;;YAEV,4BACC,8OAAC;gBACC,IAAI;gBACJ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gBACA,QAAQ,iBAAiB;gBAE3B,MAAM,QAAQ,UAAU;0BAEvB;;;;;;;;;;;;AAKX;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 762, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/components/trip-planner/TripMap.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useRef, useState } from 'react'\nimport { Loader } from '@googlemaps/js-api-loader'\nimport { Button } from '@/components/ui/Button'\nimport { Input } from '@/components/ui/Input'\nimport { Badge } from '@/components/ui/Badge'\nimport { \n  MapPinIcon, \n  PlusIcon, \n  TrashIcon, \n  SearchIcon,\n  MapIcon,\n  ListIcon,\n  NavigationIcon\n} from 'lucide-react'\nimport toast from 'react-hot-toast'\n\ninterface TripPin {\n  id: string\n  name: string\n  description?: string\n  latitude: number\n  longitude: number\n  category: 'accommodation' | 'restaurant' | 'attraction' | 'transport' | 'shopping' | 'other'\n  priority: 'high' | 'medium' | 'low'\n  visited: boolean\n  notes?: string\n  estimatedCost?: number\n  estimatedDuration?: number // in minutes\n}\n\ninterface TripMapProps {\n  tripId: string\n  destination: {\n    city?: string\n    country: string\n    latitude?: number\n    longitude?: number\n  }\n  pins: TripPin[]\n  onPinsChange: (pins: TripPin[]) => void\n  readonly?: boolean\n}\n\nconst categoryColors = {\n  accommodation: '#e74c3c',\n  restaurant: '#f39c12',\n  attraction: '#3498db',\n  transport: '#9b59b6',\n  shopping: '#2ecc71',\n  other: '#95a5a6'\n}\n\nconst categoryIcons = {\n  accommodation: '🏨',\n  restaurant: '🍽️',\n  attraction: '🎯',\n  transport: '🚗',\n  shopping: '🛍️',\n  other: '📍'\n}\n\nexport default function TripMap({ tripId, destination, pins, onPinsChange, readonly = false }: TripMapProps) {\n  const mapRef = useRef<HTMLDivElement>(null)\n  const mapInstanceRef = useRef<google.maps.Map | null>(null)\n  const markersRef = useRef<google.maps.Marker[]>([])\n  const [isLoaded, setIsLoaded] = useState(false)\n  const [isLoading, setIsLoading] = useState(true)\n  const [viewMode, setViewMode] = useState<'map' | 'list'>('map')\n  const [searchQuery, setSearchQuery] = useState('')\n  const [selectedPin, setSelectedPin] = useState<TripPin | null>(null)\n  const [showAddForm, setShowAddForm] = useState(false)\n  const [newPin, setNewPin] = useState({\n    name: '',\n    description: '',\n    category: 'attraction' as TripPin['category'],\n    priority: 'medium' as TripPin['priority'],\n    notes: '',\n    estimatedCost: '',\n    estimatedDuration: ''\n  })\n\n  useEffect(() => {\n    initializeMap()\n  }, [])\n\n  useEffect(() => {\n    if (isLoaded && mapInstanceRef.current) {\n      updateMapMarkers()\n    }\n  }, [pins, isLoaded])\n\n  const initializeMap = async () => {\n    try {\n      const apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY\n\n      if (!apiKey) {\n        setIsLoading(false)\n        return\n      }\n\n      const loader = new Loader({\n        apiKey,\n        version: 'weekly',\n        libraries: ['places', 'geometry']\n      })\n\n      await loader.load()\n      \n      if (!mapRef.current) return\n\n      // Default to destination coordinates or fallback\n      const defaultLat = destination.latitude || 0\n      const defaultLng = destination.longitude || 0\n\n      const map = new google.maps.Map(mapRef.current, {\n        center: { lat: defaultLat, lng: defaultLng },\n        zoom: destination.latitude ? 12 : 2,\n        mapTypeControl: true,\n        streetViewControl: true,\n        fullscreenControl: true,\n        zoomControl: true,\n        styles: [\n          {\n            featureType: 'poi',\n            elementType: 'labels',\n            stylers: [{ visibility: 'on' }]\n          }\n        ]\n      })\n\n      mapInstanceRef.current = map\n\n      // Add click listener for adding new pins\n      if (!readonly) {\n        map.addListener('click', (event: google.maps.MapMouseEvent) => {\n          if (event.latLng) {\n            handleMapClick(event.latLng.lat(), event.latLng.lng())\n          }\n        })\n      }\n\n      // If no destination coordinates, try to geocode the destination\n      if (!destination.latitude && destination.city && destination.country) {\n        geocodeDestination(map)\n      }\n\n      setIsLoaded(true)\n    } catch (error) {\n      console.error('Error loading Google Maps:', error)\n      toast.error('Failed to load map. Please check your internet connection.')\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const geocodeDestination = async (map: google.maps.Map) => {\n    const geocoder = new google.maps.Geocoder()\n    const address = `${destination.city}, ${destination.country}`\n    \n    try {\n      const result = await geocoder.geocode({ address })\n      if (result.results[0]) {\n        const location = result.results[0].geometry.location\n        map.setCenter(location)\n        map.setZoom(12)\n      }\n    } catch (error) {\n      console.error('Geocoding failed:', error)\n    }\n  }\n\n  const updateMapMarkers = () => {\n    if (!mapInstanceRef.current) return\n\n    // Clear existing markers\n    markersRef.current.forEach(marker => marker.setMap(null))\n    markersRef.current = []\n\n    // Add markers for each pin\n    pins.forEach(pin => {\n      const marker = new google.maps.Marker({\n        position: { lat: pin.latitude, lng: pin.longitude },\n        map: mapInstanceRef.current,\n        title: pin.name,\n        icon: {\n          url: `data:image/svg+xml;charset=UTF-8,${encodeURIComponent(createCustomMarkerSVG(pin))}`,\n          scaledSize: new google.maps.Size(40, 40),\n          anchor: new google.maps.Point(20, 40)\n        }\n      })\n\n      // Add info window\n      const infoWindow = new google.maps.InfoWindow({\n        content: createInfoWindowContent(pin)\n      })\n\n      marker.addListener('click', () => {\n        // Close other info windows\n        markersRef.current.forEach(m => {\n          const iw = (m as any).infoWindow\n          if (iw) iw.close()\n        })\n        \n        infoWindow.open(mapInstanceRef.current, marker)\n        setSelectedPin(pin)\n      })\n\n      ;(marker as any).infoWindow = infoWindow\n      markersRef.current.push(marker)\n    })\n  }\n\n  const createCustomMarkerSVG = (pin: TripPin) => {\n    const color = categoryColors[pin.category]\n    const opacity = pin.visited ? 0.6 : 1\n    \n    return `\n      <svg width=\"40\" height=\"40\" viewBox=\"0 0 40 40\" xmlns=\"http://www.w3.org/2000/svg\">\n        <circle cx=\"20\" cy=\"20\" r=\"15\" fill=\"${color}\" opacity=\"${opacity}\" stroke=\"white\" stroke-width=\"2\"/>\n        <text x=\"20\" y=\"25\" text-anchor=\"middle\" font-size=\"12\" fill=\"white\">${categoryIcons[pin.category]}</text>\n      </svg>\n    `\n  }\n\n  const createInfoWindowContent = (pin: TripPin) => {\n    return `\n      <div style=\"max-width: 250px; padding: 8px;\">\n        <h3 style=\"margin: 0 0 8px 0; font-size: 16px; font-weight: bold;\">${pin.name}</h3>\n        ${pin.description ? `<p style=\"margin: 0 0 8px 0; font-size: 14px; color: #666;\">${pin.description}</p>` : ''}\n        <div style=\"display: flex; gap: 8px; margin-bottom: 8px;\">\n          <span style=\"background: ${categoryColors[pin.category]}; color: white; padding: 2px 6px; border-radius: 4px; font-size: 12px;\">\n            ${pin.category}\n          </span>\n          <span style=\"background: #f0f0f0; padding: 2px 6px; border-radius: 4px; font-size: 12px;\">\n            ${pin.priority} priority\n          </span>\n        </div>\n        ${pin.estimatedCost ? `<p style=\"margin: 4px 0; font-size: 12px;\"><strong>Est. Cost:</strong> $${pin.estimatedCost}</p>` : ''}\n        ${pin.estimatedDuration ? `<p style=\"margin: 4px 0; font-size: 12px;\"><strong>Duration:</strong> ${pin.estimatedDuration} min</p>` : ''}\n        ${pin.notes ? `<p style=\"margin: 4px 0; font-size: 12px;\"><strong>Notes:</strong> ${pin.notes}</p>` : ''}\n        ${!readonly ? `\n          <div style=\"margin-top: 8px; display: flex; gap: 8px;\">\n            <button onclick=\"window.editPin('${pin.id}')\" style=\"background: #3498db; color: white; border: none; padding: 4px 8px; border-radius: 4px; font-size: 12px; cursor: pointer;\">\n              Edit\n            </button>\n            <button onclick=\"window.deletePin('${pin.id}')\" style=\"background: #e74c3c; color: white; border: none; padding: 4px 8px; border-radius: 4px; font-size: 12px; cursor: pointer;\">\n              Delete\n            </button>\n          </div>\n        ` : ''}\n      </div>\n    `\n  }\n\n  const handleMapClick = (lat: number, lng: number) => {\n    if (readonly) return\n    \n    // Set the coordinates for the new pin\n    setNewPin(prev => ({ ...prev, latitude: lat, longitude: lng }))\n    setShowAddForm(true)\n  }\n\n  const addPin = () => {\n    if (!newPin.name.trim()) {\n      toast.error('Please enter a name for the pin')\n      return\n    }\n\n    const pin: TripPin = {\n      id: Date.now().toString(),\n      name: newPin.name.trim(),\n      description: newPin.description.trim() || undefined,\n      latitude: (newPin as any).latitude,\n      longitude: (newPin as any).longitude,\n      category: newPin.category,\n      priority: newPin.priority,\n      visited: false,\n      notes: newPin.notes.trim() || undefined,\n      estimatedCost: newPin.estimatedCost ? parseFloat(newPin.estimatedCost) : undefined,\n      estimatedDuration: newPin.estimatedDuration ? parseInt(newPin.estimatedDuration) : undefined\n    }\n\n    onPinsChange([...pins, pin])\n    \n    // Reset form\n    setNewPin({\n      name: '',\n      description: '',\n      category: 'attraction',\n      priority: 'medium',\n      notes: '',\n      estimatedCost: '',\n      estimatedDuration: ''\n    })\n    setShowAddForm(false)\n    toast.success('Pin added successfully!')\n  }\n\n  const deletePin = (pinId: string) => {\n    onPinsChange(pins.filter(pin => pin.id !== pinId))\n    toast.success('Pin deleted successfully!')\n  }\n\n  const togglePinVisited = (pinId: string) => {\n    onPinsChange(pins.map(pin => \n      pin.id === pinId ? { ...pin, visited: !pin.visited } : pin\n    ))\n  }\n\n  // Expose functions to global scope for info window buttons\n  useEffect(() => {\n    ;(window as any).editPin = (pinId: string) => {\n      const pin = pins.find(p => p.id === pinId)\n      if (pin) {\n        setSelectedPin(pin)\n        // TODO: Implement edit functionality\n        toast.info('Edit functionality coming soon!')\n      }\n    }\n\n    ;(window as any).deletePin = (pinId: string) => {\n      if (confirm('Are you sure you want to delete this pin?')) {\n        deletePin(pinId)\n      }\n    }\n\n    return () => {\n      delete (window as any).editPin\n      delete (window as any).deletePin\n    }\n  }, [pins])\n\n  const filteredPins = pins.filter(pin =>\n    pin.name.toLowerCase().includes(searchQuery.toLowerCase()) ||\n    pin.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||\n    pin.category.toLowerCase().includes(searchQuery.toLowerCase())\n  )\n\n  if (isLoading) {\n    return (\n      <div className=\"flex items-center justify-center h-96 bg-gray-50 rounded-lg\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600\">Loading map...</p>\n        </div>\n      </div>\n    )\n  }\n\n  // Show setup instructions if no API key\n  if (!process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY) {\n    return (\n      <div className=\"space-y-4\">\n        <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\">\n          <div className=\"flex items-center space-x-4\">\n            <h3 className=\"text-lg font-semibold text-gray-900\">Trip Map</h3>\n            <Badge variant=\"secondary\">{pins.length} pins</Badge>\n          </div>\n        </div>\n\n        <div className=\"bg-yellow-50 border border-yellow-200 rounded-lg p-6\">\n          <div className=\"flex items-start\">\n            <MapIcon className=\"h-6 w-6 text-yellow-600 mt-1 mr-3\" />\n            <div>\n              <h4 className=\"text-lg font-medium text-yellow-800 mb-2\">Google Maps Setup Required</h4>\n              <p className=\"text-yellow-700 mb-4\">\n                To use the interactive map feature with custom drop pins, you need to set up a Google Maps API key.\n              </p>\n              <div className=\"space-y-2 text-sm text-yellow-700\">\n                <p><strong>Steps to set up:</strong></p>\n                <ol className=\"list-decimal list-inside space-y-1 ml-4\">\n                  <li>Go to <a href=\"https://console.cloud.google.com/google/maps-apis/\" target=\"_blank\" rel=\"noopener noreferrer\" className=\"underline hover:text-yellow-800\">Google Cloud Console</a></li>\n                  <li>Create a new project or select an existing one</li>\n                  <li>Enable the \"Maps JavaScript API\" and \"Places API\"</li>\n                  <li>Create an API key in the \"Credentials\" section</li>\n                  <li>Add the API key to your .env.local file as NEXT_PUBLIC_GOOGLE_MAPS_API_KEY</li>\n                  <li>Restart your development server</li>\n                </ol>\n              </div>\n              <div className=\"mt-4\">\n                <Button\n                  onClick={() => window.open('https://console.cloud.google.com/google/maps-apis/', '_blank')}\n                  className=\"bg-yellow-600 hover:bg-yellow-700 text-white\"\n                >\n                  <NavigationIcon className=\"h-4 w-4 mr-2\" />\n                  Set Up Google Maps API\n                </Button>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Fallback List View */}\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200\">\n          <div className=\"p-4 border-b border-gray-200\">\n            <h4 className=\"font-medium text-gray-900\">Places to Visit (List View)</h4>\n            <p className=\"text-sm text-gray-600 mt-1\">Add places manually below. Map view will be available once Google Maps is configured.</p>\n          </div>\n          <div className=\"divide-y divide-gray-200\">\n            {pins.map((pin) => (\n              <div key={pin.id} className=\"p-4 hover:bg-gray-50\">\n                <div className=\"flex items-start justify-between\">\n                  <div className=\"flex-1\">\n                    <div className=\"flex items-center space-x-3\">\n                      <span className=\"text-lg\">{categoryIcons[pin.category]}</span>\n                      <div>\n                        <h5 className={`font-medium ${pin.visited ? 'line-through text-gray-500' : 'text-gray-900'}`}>\n                          {pin.name}\n                        </h5>\n                        {pin.description && (\n                          <p className=\"text-sm text-gray-600 mt-1\">{pin.description}</p>\n                        )}\n                        <div className=\"flex items-center space-x-2 mt-2\">\n                          <Badge\n                            style={{ backgroundColor: categoryColors[pin.category], color: 'white' }}\n                            className=\"text-xs\"\n                          >\n                            {pin.category}\n                          </Badge>\n                          <Badge variant=\"secondary\" className=\"text-xs\">\n                            {pin.priority} priority\n                          </Badge>\n                          {pin.visited && (\n                            <Badge variant=\"secondary\" className=\"text-xs bg-green-100 text-green-800\">\n                              Visited\n                            </Badge>\n                          )}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n\n                  {!readonly && (\n                    <div className=\"flex items-center space-x-2\">\n                      <Button\n                        size=\"sm\"\n                        variant=\"outline\"\n                        onClick={() => togglePinVisited(pin.id)}\n                        className=\"text-xs\"\n                      >\n                        {pin.visited ? 'Unmark' : 'Mark Visited'}\n                      </Button>\n                      <Button\n                        size=\"sm\"\n                        variant=\"outline\"\n                        onClick={() => deletePin(pin.id)}\n                        className=\"text-red-600 hover:text-red-700 hover:bg-red-50\"\n                      >\n                        <TrashIcon className=\"h-3 w-3\" />\n                      </Button>\n                    </div>\n                  )}\n                </div>\n\n                {(pin.estimatedCost || pin.estimatedDuration || pin.notes) && (\n                  <div className=\"mt-3 text-sm text-gray-600 space-y-1\">\n                    {pin.estimatedCost && (\n                      <p><strong>Est. Cost:</strong> ${pin.estimatedCost}</p>\n                    )}\n                    {pin.estimatedDuration && (\n                      <p><strong>Duration:</strong> {pin.estimatedDuration} minutes</p>\n                    )}\n                    {pin.notes && (\n                      <p><strong>Notes:</strong> {pin.notes}</p>\n                    )}\n                  </div>\n                )}\n              </div>\n            ))}\n\n            {pins.length === 0 && (\n              <div className=\"p-8 text-center text-gray-500\">\n                <MapPinIcon className=\"mx-auto h-12 w-12 text-gray-300 mb-3\" />\n                <p className=\"text-sm font-medium\">No places added yet</p>\n                <p className=\"text-xs text-gray-400 mt-1\">Click \"Add Pin\" below to add places to visit</p>\n              </div>\n            )}\n          </div>\n\n          {!readonly && (\n            <div className=\"p-4 border-t border-gray-200\">\n              <Button\n                onClick={() => {\n                  // For fallback, we'll set dummy coordinates\n                  setNewPin(prev => ({ ...prev, latitude: 0, longitude: 0 }))\n                  setShowAddForm(true)\n                }}\n                className=\"w-full\"\n              >\n                <PlusIcon className=\"h-4 w-4 mr-2\" />\n                Add Place to Visit\n              </Button>\n            </div>\n          )}\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"space-y-4\">\n      {/* Header Controls */}\n      <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\">\n        <div className=\"flex items-center space-x-4\">\n          <h3 className=\"text-lg font-semibold text-gray-900\">Trip Map</h3>\n          <Badge variant=\"secondary\">{pins.length} pins</Badge>\n        </div>\n        \n        <div className=\"flex items-center space-x-3\">\n          {/* Search */}\n          <div className=\"relative\">\n            <SearchIcon className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n            <Input\n              placeholder=\"Search pins...\"\n              value={searchQuery}\n              onChange={(e) => setSearchQuery(e.target.value)}\n              className=\"pl-10 w-48\"\n            />\n          </div>\n          \n          {/* View Mode Toggle */}\n          <div className=\"flex items-center bg-gray-100 rounded-lg p-1\">\n            <Button\n              variant={viewMode === 'map' ? 'default' : 'ghost'}\n              size=\"sm\"\n              onClick={() => setViewMode('map')}\n              className=\"h-8\"\n            >\n              <MapIcon className=\"h-4 w-4\" />\n            </Button>\n            <Button\n              variant={viewMode === 'list' ? 'default' : 'ghost'}\n              size=\"sm\"\n              onClick={() => setViewMode('list')}\n              className=\"h-8\"\n            >\n              <ListIcon className=\"h-4 w-4\" />\n            </Button>\n          </div>\n        </div>\n      </div>\n\n      {/* Map or List View */}\n      {viewMode === 'map' ? (\n        <div className=\"relative\">\n          <div ref={mapRef} className=\"w-full h-96 rounded-lg shadow-sm border border-gray-200\" />\n          \n          {!readonly && (\n            <div className=\"absolute top-4 right-4\">\n              <Button\n                onClick={() => setShowAddForm(true)}\n                className=\"bg-white shadow-lg hover:bg-gray-50 text-gray-700 border border-gray-200\"\n              >\n                <PlusIcon className=\"h-4 w-4 mr-2\" />\n                Add Pin\n              </Button>\n            </div>\n          )}\n        </div>\n      ) : (\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200\">\n          <div className=\"p-4 border-b border-gray-200\">\n            <h4 className=\"font-medium text-gray-900\">Places to Visit</h4>\n          </div>\n          <div className=\"divide-y divide-gray-200\">\n            {filteredPins.map((pin) => (\n              <div key={pin.id} className=\"p-4 hover:bg-gray-50\">\n                <div className=\"flex items-start justify-between\">\n                  <div className=\"flex-1\">\n                    <div className=\"flex items-center space-x-3\">\n                      <span className=\"text-lg\">{categoryIcons[pin.category]}</span>\n                      <div>\n                        <h5 className={`font-medium ${pin.visited ? 'line-through text-gray-500' : 'text-gray-900'}`}>\n                          {pin.name}\n                        </h5>\n                        {pin.description && (\n                          <p className=\"text-sm text-gray-600 mt-1\">{pin.description}</p>\n                        )}\n                        <div className=\"flex items-center space-x-2 mt-2\">\n                          <Badge \n                            style={{ backgroundColor: categoryColors[pin.category], color: 'white' }}\n                            className=\"text-xs\"\n                          >\n                            {pin.category}\n                          </Badge>\n                          <Badge variant=\"secondary\" className=\"text-xs\">\n                            {pin.priority} priority\n                          </Badge>\n                          {pin.visited && (\n                            <Badge variant=\"secondary\" className=\"text-xs bg-green-100 text-green-800\">\n                              Visited\n                            </Badge>\n                          )}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  \n                  {!readonly && (\n                    <div className=\"flex items-center space-x-2\">\n                      <Button\n                        size=\"sm\"\n                        variant=\"outline\"\n                        onClick={() => togglePinVisited(pin.id)}\n                        className=\"text-xs\"\n                      >\n                        {pin.visited ? 'Unmark' : 'Mark Visited'}\n                      </Button>\n                      <Button\n                        size=\"sm\"\n                        variant=\"outline\"\n                        onClick={() => deletePin(pin.id)}\n                        className=\"text-red-600 hover:text-red-700 hover:bg-red-50\"\n                      >\n                        <TrashIcon className=\"h-3 w-3\" />\n                      </Button>\n                    </div>\n                  )}\n                </div>\n                \n                {(pin.estimatedCost || pin.estimatedDuration || pin.notes) && (\n                  <div className=\"mt-3 text-sm text-gray-600 space-y-1\">\n                    {pin.estimatedCost && (\n                      <p><strong>Est. Cost:</strong> ${pin.estimatedCost}</p>\n                    )}\n                    {pin.estimatedDuration && (\n                      <p><strong>Duration:</strong> {pin.estimatedDuration} minutes</p>\n                    )}\n                    {pin.notes && (\n                      <p><strong>Notes:</strong> {pin.notes}</p>\n                    )}\n                  </div>\n                )}\n              </div>\n            ))}\n            \n            {filteredPins.length === 0 && (\n              <div className=\"p-8 text-center text-gray-500\">\n                {searchQuery ? 'No pins match your search.' : 'No pins added yet. Click on the map to add places to visit!'}\n              </div>\n            )}\n          </div>\n        </div>\n      )}\n\n      {/* Add Pin Modal */}\n      {showAddForm && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n          <div className=\"bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto\">\n            <div className=\"p-6\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Add New Pin</h3>\n              \n              <div className=\"space-y-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Name *\n                  </label>\n                  <Input\n                    value={newPin.name}\n                    onChange={(e) => setNewPin(prev => ({ ...prev, name: e.target.value }))}\n                    placeholder=\"e.g., Eiffel Tower\"\n                  />\n                </div>\n                \n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Description\n                  </label>\n                  <Input\n                    value={newPin.description}\n                    onChange={(e) => setNewPin(prev => ({ ...prev, description: e.target.value }))}\n                    placeholder=\"Brief description...\"\n                  />\n                </div>\n                \n                <div className=\"grid grid-cols-2 gap-4\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      Category\n                    </label>\n                    <select\n                      value={newPin.category}\n                      onChange={(e) => setNewPin(prev => ({ ...prev, category: e.target.value as TripPin['category'] }))}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    >\n                      <option value=\"attraction\">Attraction</option>\n                      <option value=\"restaurant\">Restaurant</option>\n                      <option value=\"accommodation\">Accommodation</option>\n                      <option value=\"transport\">Transport</option>\n                      <option value=\"shopping\">Shopping</option>\n                      <option value=\"other\">Other</option>\n                    </select>\n                  </div>\n                  \n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      Priority\n                    </label>\n                    <select\n                      value={newPin.priority}\n                      onChange={(e) => setNewPin(prev => ({ ...prev, priority: e.target.value as TripPin['priority'] }))}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    >\n                      <option value=\"high\">High</option>\n                      <option value=\"medium\">Medium</option>\n                      <option value=\"low\">Low</option>\n                    </select>\n                  </div>\n                </div>\n                \n                <div className=\"grid grid-cols-2 gap-4\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      Est. Cost ($)\n                    </label>\n                    <Input\n                      type=\"number\"\n                      value={newPin.estimatedCost}\n                      onChange={(e) => setNewPin(prev => ({ ...prev, estimatedCost: e.target.value }))}\n                      placeholder=\"0\"\n                    />\n                  </div>\n                  \n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      Duration (min)\n                    </label>\n                    <Input\n                      type=\"number\"\n                      value={newPin.estimatedDuration}\n                      onChange={(e) => setNewPin(prev => ({ ...prev, estimatedDuration: e.target.value }))}\n                      placeholder=\"60\"\n                    />\n                  </div>\n                </div>\n                \n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Notes\n                  </label>\n                  <textarea\n                    value={newPin.notes}\n                    onChange={(e) => setNewPin(prev => ({ ...prev, notes: e.target.value }))}\n                    placeholder=\"Additional notes...\"\n                    rows={3}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  />\n                </div>\n              </div>\n              \n              <div className=\"flex justify-end space-x-3 mt-6\">\n                <Button\n                  variant=\"outline\"\n                  onClick={() => setShowAddForm(false)}\n                >\n                  Cancel\n                </Button>\n                <Button onClick={addPin}>\n                  Add Pin\n                </Button>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AASA;AAhBA;;;;;;;;;AA6CA,MAAM,iBAAiB;IACrB,eAAe;IACf,YAAY;IACZ,YAAY;IACZ,WAAW;IACX,UAAU;IACV,OAAO;AACT;AAEA,MAAM,gBAAgB;IACpB,eAAe;IACf,YAAY;IACZ,YAAY;IACZ,WAAW;IACX,UAAU;IACV,OAAO;AACT;AAEe,SAAS,QAAQ,EAAE,MAAM,EAAE,WAAW,EAAE,IAAI,EAAE,YAAY,EAAE,WAAW,KAAK,EAAgB;IACzG,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IACtC,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAA0B;IACtD,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAwB,EAAE;IAClD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IACzD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IAC/D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACnC,MAAM;QACN,aAAa;QACb,UAAU;QACV,UAAU;QACV,OAAO;QACP,eAAe;QACf,mBAAmB;IACrB;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,YAAY,eAAe,OAAO,EAAE;YACtC;QACF;IACF,GAAG;QAAC;QAAM;KAAS;IAEnB,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM;YAEN,uCAAa;;YAGb;YAEA,MAAM,SAAS,IAAI,qKAAA,CAAA,SAAM,CAAC;gBACxB;gBACA,SAAS;gBACT,WAAW;oBAAC;oBAAU;iBAAW;YACnC;YAEA,MAAM,OAAO,IAAI;YAEjB,IAAI,CAAC,OAAO,OAAO,EAAE;YAErB,iDAAiD;YACjD,MAAM,aAAa,YAAY,QAAQ,IAAI;YAC3C,MAAM,aAAa,YAAY,SAAS,IAAI;YAE5C,MAAM,MAAM,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,OAAO,EAAE;gBAC9C,QAAQ;oBAAE,KAAK;oBAAY,KAAK;gBAAW;gBAC3C,MAAM,YAAY,QAAQ,GAAG,KAAK;gBAClC,gBAAgB;gBAChB,mBAAmB;gBACnB,mBAAmB;gBACnB,aAAa;gBACb,QAAQ;oBACN;wBACE,aAAa;wBACb,aAAa;wBACb,SAAS;4BAAC;gCAAE,YAAY;4BAAK;yBAAE;oBACjC;iBACD;YACH;YAEA,eAAe,OAAO,GAAG;YAEzB,yCAAyC;YACzC,IAAI,CAAC,UAAU;gBACb,IAAI,WAAW,CAAC,SAAS,CAAC;oBACxB,IAAI,MAAM,MAAM,EAAE;wBAChB,eAAe,MAAM,MAAM,CAAC,GAAG,IAAI,MAAM,MAAM,CAAC,GAAG;oBACrD;gBACF;YACF;YAEA,gEAAgE;YAChE,IAAI,CAAC,YAAY,QAAQ,IAAI,YAAY,IAAI,IAAI,YAAY,OAAO,EAAE;gBACpE,mBAAmB;YACrB;YAEA,YAAY;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,qBAAqB,OAAO;QAChC,MAAM,WAAW,IAAI,OAAO,IAAI,CAAC,QAAQ;QACzC,MAAM,UAAU,GAAG,YAAY,IAAI,CAAC,EAAE,EAAE,YAAY,OAAO,EAAE;QAE7D,IAAI;YACF,MAAM,SAAS,MAAM,SAAS,OAAO,CAAC;gBAAE;YAAQ;YAChD,IAAI,OAAO,OAAO,CAAC,EAAE,EAAE;gBACrB,MAAM,WAAW,OAAO,OAAO,CAAC,EAAE,CAAC,QAAQ,CAAC,QAAQ;gBACpD,IAAI,SAAS,CAAC;gBACd,IAAI,OAAO,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;QACrC;IACF;IAEA,MAAM,mBAAmB;QACvB,IAAI,CAAC,eAAe,OAAO,EAAE;QAE7B,yBAAyB;QACzB,WAAW,OAAO,CAAC,OAAO,CAAC,CAAA,SAAU,OAAO,MAAM,CAAC;QACnD,WAAW,OAAO,GAAG,EAAE;QAEvB,2BAA2B;QAC3B,KAAK,OAAO,CAAC,CAAA;YACX,MAAM,SAAS,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC;gBACpC,UAAU;oBAAE,KAAK,IAAI,QAAQ;oBAAE,KAAK,IAAI,SAAS;gBAAC;gBAClD,KAAK,eAAe,OAAO;gBAC3B,OAAO,IAAI,IAAI;gBACf,MAAM;oBACJ,KAAK,CAAC,iCAAiC,EAAE,mBAAmB,sBAAsB,OAAO;oBACzF,YAAY,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI;oBACrC,QAAQ,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI;gBACpC;YACF;YAEA,kBAAkB;YAClB,MAAM,aAAa,IAAI,OAAO,IAAI,CAAC,UAAU,CAAC;gBAC5C,SAAS,wBAAwB;YACnC;YAEA,OAAO,WAAW,CAAC,SAAS;gBAC1B,2BAA2B;gBAC3B,WAAW,OAAO,CAAC,OAAO,CAAC,CAAA;oBACzB,MAAM,KAAK,AAAC,EAAU,UAAU;oBAChC,IAAI,IAAI,GAAG,KAAK;gBAClB;gBAEA,WAAW,IAAI,CAAC,eAAe,OAAO,EAAE;gBACxC,eAAe;YACjB;YAEE,OAAe,UAAU,GAAG;YAC9B,WAAW,OAAO,CAAC,IAAI,CAAC;QAC1B;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,MAAM,QAAQ,cAAc,CAAC,IAAI,QAAQ,CAAC;QAC1C,MAAM,UAAU,IAAI,OAAO,GAAG,MAAM;QAEpC,OAAO,CAAC;;6CAEiC,EAAE,MAAM,WAAW,EAAE,QAAQ;6EACG,EAAE,aAAa,CAAC,IAAI,QAAQ,CAAC,CAAC;;IAEvG,CAAC;IACH;IAEA,MAAM,0BAA0B,CAAC;QAC/B,OAAO,CAAC;;2EAE+D,EAAE,IAAI,IAAI,CAAC;QAC9E,EAAE,IAAI,WAAW,GAAG,CAAC,4DAA4D,EAAE,IAAI,WAAW,CAAC,IAAI,CAAC,GAAG,GAAG;;mCAEnF,EAAE,cAAc,CAAC,IAAI,QAAQ,CAAC,CAAC;YACtD,EAAE,IAAI,QAAQ,CAAC;;;YAGf,EAAE,IAAI,QAAQ,CAAC;;;QAGnB,EAAE,IAAI,aAAa,GAAG,CAAC,wEAAwE,EAAE,IAAI,aAAa,CAAC,IAAI,CAAC,GAAG,GAAG;QAC9H,EAAE,IAAI,iBAAiB,GAAG,CAAC,sEAAsE,EAAE,IAAI,iBAAiB,CAAC,QAAQ,CAAC,GAAG,GAAG;QACxI,EAAE,IAAI,KAAK,GAAG,CAAC,mEAAmE,EAAE,IAAI,KAAK,CAAC,IAAI,CAAC,GAAG,GAAG;QACzG,EAAE,CAAC,WAAW,CAAC;;6CAEsB,EAAE,IAAI,EAAE,CAAC;;;+CAGP,EAAE,IAAI,EAAE,CAAC;;;;QAIhD,CAAC,GAAG,GAAG;;IAEX,CAAC;IACH;IAEA,MAAM,iBAAiB,CAAC,KAAa;QACnC,IAAI,UAAU;QAEd,sCAAsC;QACtC,UAAU,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,UAAU;gBAAK,WAAW;YAAI,CAAC;QAC7D,eAAe;IACjB;IAEA,MAAM,SAAS;QACb,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,IAAI;YACvB,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,MAAM,MAAe;YACnB,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,MAAM,OAAO,IAAI,CAAC,IAAI;YACtB,aAAa,OAAO,WAAW,CAAC,IAAI,MAAM;YAC1C,UAAU,AAAC,OAAe,QAAQ;YAClC,WAAW,AAAC,OAAe,SAAS;YACpC,UAAU,OAAO,QAAQ;YACzB,UAAU,OAAO,QAAQ;YACzB,SAAS;YACT,OAAO,OAAO,KAAK,CAAC,IAAI,MAAM;YAC9B,eAAe,OAAO,aAAa,GAAG,WAAW,OAAO,aAAa,IAAI;YACzE,mBAAmB,OAAO,iBAAiB,GAAG,SAAS,OAAO,iBAAiB,IAAI;QACrF;QAEA,aAAa;eAAI;YAAM;SAAI;QAE3B,aAAa;QACb,UAAU;YACR,MAAM;YACN,aAAa;YACb,UAAU;YACV,UAAU;YACV,OAAO;YACP,eAAe;YACf,mBAAmB;QACrB;QACA,eAAe;QACf,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC;IAChB;IAEA,MAAM,YAAY,CAAC;QACjB,aAAa,KAAK,MAAM,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;QAC3C,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC;IAChB;IAEA,MAAM,mBAAmB,CAAC;QACxB,aAAa,KAAK,GAAG,CAAC,CAAA,MACpB,IAAI,EAAE,KAAK,QAAQ;gBAAE,GAAG,GAAG;gBAAE,SAAS,CAAC,IAAI,OAAO;YAAC,IAAI;IAE3D;IAEA,2DAA2D;IAC3D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;;QACN,OAAe,OAAO,GAAG,CAAC;YAC1B,MAAM,MAAM,KAAK,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YACpC,IAAI,KAAK;gBACP,eAAe;gBACf,qCAAqC;gBACrC,uJAAA,CAAA,UAAK,CAAC,IAAI,CAAC;YACb;QACF;QAEE,OAAe,SAAS,GAAG,CAAC;YAC5B,IAAI,QAAQ,8CAA8C;gBACxD,UAAU;YACZ;QACF;QAEA,OAAO;YACL,OAAO,AAAC,OAAe,OAAO;YAC9B,OAAO,AAAC,OAAe,SAAS;QAClC;IACF,GAAG;QAAC;KAAK;IAET,MAAM,eAAe,KAAK,MAAM,CAAC,CAAA,MAC/B,IAAI,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OACvD,IAAI,WAAW,EAAE,cAAc,SAAS,YAAY,WAAW,OAC/D,IAAI,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;IAG7D,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,wCAAwC;IACxC,uCAAkD;;IAmJlD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;0CACpD,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;;oCAAa,KAAK,MAAM;oCAAC;;;;;;;;;;;;;kCAG1C,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0MAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;kDACtB,8OAAC,iIAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wCAC9C,WAAU;;;;;;;;;;;;0CAKd,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAS,aAAa,QAAQ,YAAY;wCAC1C,MAAK;wCACL,SAAS,IAAM,YAAY;wCAC3B,WAAU;kDAEV,cAAA,8OAAC,oMAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;;;;;;kDAErB,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAS,aAAa,SAAS,YAAY;wCAC3C,MAAK;wCACL,SAAS,IAAM,YAAY;wCAC3B,WAAU;kDAEV,cAAA,8OAAC,sMAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAO3B,aAAa,sBACZ,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,KAAK;wBAAQ,WAAU;;;;;;oBAE3B,CAAC,0BACA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAS,IAAM,eAAe;4BAC9B,WAAU;;8CAEV,8OAAC,sMAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;qCAO7C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAG,WAAU;sCAA4B;;;;;;;;;;;kCAE5C,8OAAC;wBAAI,WAAU;;4BACZ,aAAa,GAAG,CAAC,CAAC,oBACjB,8OAAC;oCAAiB,WAAU;;sDAC1B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAW,aAAa,CAAC,IAAI,QAAQ,CAAC;;;;;;0EACtD,8OAAC;;kFACC,8OAAC;wEAAG,WAAW,CAAC,YAAY,EAAE,IAAI,OAAO,GAAG,+BAA+B,iBAAiB;kFACzF,IAAI,IAAI;;;;;;oEAEV,IAAI,WAAW,kBACd,8OAAC;wEAAE,WAAU;kFAA8B,IAAI,WAAW;;;;;;kFAE5D,8OAAC;wEAAI,WAAU;;0FACb,8OAAC,iIAAA,CAAA,QAAK;gFACJ,OAAO;oFAAE,iBAAiB,cAAc,CAAC,IAAI,QAAQ,CAAC;oFAAE,OAAO;gFAAQ;gFACvE,WAAU;0FAET,IAAI,QAAQ;;;;;;0FAEf,8OAAC,iIAAA,CAAA,QAAK;gFAAC,SAAQ;gFAAY,WAAU;;oFAClC,IAAI,QAAQ;oFAAC;;;;;;;4EAEf,IAAI,OAAO,kBACV,8OAAC,iIAAA,CAAA,QAAK;gFAAC,SAAQ;gFAAY,WAAU;0FAAsC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gDASpF,CAAC,0BACA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAQ;4DACR,SAAS,IAAM,iBAAiB,IAAI,EAAE;4DACtC,WAAU;sEAET,IAAI,OAAO,GAAG,WAAW;;;;;;sEAE5B,8OAAC,kIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAQ;4DACR,SAAS,IAAM,UAAU,IAAI,EAAE;4DAC/B,WAAU;sEAEV,cAAA,8OAAC,wMAAA,CAAA,YAAS;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;wCAM5B,CAAC,IAAI,aAAa,IAAI,IAAI,iBAAiB,IAAI,IAAI,KAAK,mBACvD,8OAAC;4CAAI,WAAU;;gDACZ,IAAI,aAAa,kBAChB,8OAAC;;sEAAE,8OAAC;sEAAO;;;;;;wDAAmB;wDAAG,IAAI,aAAa;;;;;;;gDAEnD,IAAI,iBAAiB,kBACpB,8OAAC;;sEAAE,8OAAC;sEAAO;;;;;;wDAAkB;wDAAE,IAAI,iBAAiB;wDAAC;;;;;;;gDAEtD,IAAI,KAAK,kBACR,8OAAC;;sEAAE,8OAAC;sEAAO;;;;;;wDAAe;wDAAE,IAAI,KAAK;;;;;;;;;;;;;;mCA/DnC,IAAI,EAAE;;;;;4BAsEjB,aAAa,MAAM,KAAK,mBACvB,8OAAC;gCAAI,WAAU;0CACZ,cAAc,+BAA+B;;;;;;;;;;;;;;;;;;YAQvD,6BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA2C;;;;;;0CAEzD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC,iIAAA,CAAA,QAAK;gDACJ,OAAO,OAAO,IAAI;gDAClB,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,MAAM,EAAE,MAAM,CAAC,KAAK;wDAAC,CAAC;gDACrE,aAAY;;;;;;;;;;;;kDAIhB,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC,iIAAA,CAAA,QAAK;gDACJ,OAAO,OAAO,WAAW;gDACzB,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,aAAa,EAAE,MAAM,CAAC,KAAK;wDAAC,CAAC;gDAC5E,aAAY;;;;;;;;;;;;kDAIhB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,8OAAC;wDACC,OAAO,OAAO,QAAQ;wDACtB,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;oEAAE,GAAG,IAAI;oEAAE,UAAU,EAAE,MAAM,CAAC,KAAK;gEAAwB,CAAC;wDAChG,WAAU;;0EAEV,8OAAC;gEAAO,OAAM;0EAAa;;;;;;0EAC3B,8OAAC;gEAAO,OAAM;0EAAa;;;;;;0EAC3B,8OAAC;gEAAO,OAAM;0EAAgB;;;;;;0EAC9B,8OAAC;gEAAO,OAAM;0EAAY;;;;;;0EAC1B,8OAAC;gEAAO,OAAM;0EAAW;;;;;;0EACzB,8OAAC;gEAAO,OAAM;0EAAQ;;;;;;;;;;;;;;;;;;0DAI1B,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,8OAAC;wDACC,OAAO,OAAO,QAAQ;wDACtB,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;oEAAE,GAAG,IAAI;oEAAE,UAAU,EAAE,MAAM,CAAC,KAAK;gEAAwB,CAAC;wDAChG,WAAU;;0EAEV,8OAAC;gEAAO,OAAM;0EAAO;;;;;;0EACrB,8OAAC;gEAAO,OAAM;0EAAS;;;;;;0EACvB,8OAAC;gEAAO,OAAM;0EAAM;;;;;;;;;;;;;;;;;;;;;;;;kDAK1B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,8OAAC,iIAAA,CAAA,QAAK;wDACJ,MAAK;wDACL,OAAO,OAAO,aAAa;wDAC3B,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;oEAAE,GAAG,IAAI;oEAAE,eAAe,EAAE,MAAM,CAAC,KAAK;gEAAC,CAAC;wDAC9E,aAAY;;;;;;;;;;;;0DAIhB,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,8OAAC,iIAAA,CAAA,QAAK;wDACJ,MAAK;wDACL,OAAO,OAAO,iBAAiB;wDAC/B,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;oEAAE,GAAG,IAAI;oEAAE,mBAAmB,EAAE,MAAM,CAAC,KAAK;gEAAC,CAAC;wDAClF,aAAY;;;;;;;;;;;;;;;;;;kDAKlB,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC;gDACC,OAAO,OAAO,KAAK;gDACnB,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,OAAO,EAAE,MAAM,CAAC,KAAK;wDAAC,CAAC;gDACtE,aAAY;gDACZ,MAAM;gDACN,WAAU;;;;;;;;;;;;;;;;;;0CAKhB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,SAAS,IAAM,eAAe;kDAC/B;;;;;;kDAGD,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAS;kDAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUzC", "debugId": null}}, {"offset": {"line": 1881, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/app/trips/%5Bid%5D/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useParams } from 'next/navigation'\nimport { motion } from 'framer-motion'\nimport { tripPlannerService, type Trip } from '@/lib/services/trip-planner-service'\nimport { Button } from '@/components/ui/Button'\nimport { Loading } from '@/components/ui/Loading'\nimport { Badge } from '@/components/ui/Badge'\nimport TripMap from '@/components/trip-planner/TripMap'\nimport { \n  MapPinIcon, \n  CalendarIcon, \n  DollarSignIcon, \n  UsersIcon,\n  ArrowLeftIcon,\n  EditIcon,\n  ShareIcon,\n  PlusIcon,\n  ClockIcon,\n  TrendingUpIcon\n} from 'lucide-react'\nimport Link from 'next/link'\nimport toast from 'react-hot-toast'\n\nexport default function TripDetailPage() {\n  const params = useParams()\n  const tripId = params.id as string\n  \n  const [trip, setTrip] = useState<Trip | null>(null)\n  const [loading, setLoading] = useState(true)\n  const [error, setError] = useState<string | null>(null)\n  const [tripPins, setTripPins] = useState<any[]>([]) // TODO: Define proper type\n\n  useEffect(() => {\n    if (tripId) {\n      fetchTrip()\n    }\n  }, [tripId])\n\n  const fetchTrip = async () => {\n    try {\n      setLoading(true)\n      const data = await tripPlannerService.getTrip(tripId)\n      setTrip(data)\n    } catch (error) {\n      console.error('Error fetching trip:', error)\n      setError('Failed to load trip details')\n      toast.error('Failed to load trip details')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'planning': return 'bg-blue-100 text-blue-800'\n      case 'booked': return 'bg-green-100 text-green-800'\n      case 'in_progress': return 'bg-yellow-100 text-yellow-800'\n      case 'completed': return 'bg-gray-100 text-gray-800'\n      case 'cancelled': return 'bg-red-100 text-red-800'\n      default: return 'bg-gray-100 text-gray-800'\n    }\n  }\n\n  const getTripTypeColor = (type: string) => {\n    switch (type) {\n      case 'leisure': return 'bg-purple-100 text-purple-800'\n      case 'business': return 'bg-blue-100 text-blue-800'\n      case 'adventure': return 'bg-orange-100 text-orange-800'\n      case 'family': return 'bg-green-100 text-green-800'\n      case 'romantic': return 'bg-pink-100 text-pink-800'\n      case 'solo': return 'bg-indigo-100 text-indigo-800'\n      case 'group': return 'bg-yellow-100 text-yellow-800'\n      default: return 'bg-gray-100 text-gray-800'\n    }\n  }\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    })\n  }\n\n  const calculateDuration = () => {\n    if (!trip) return 0\n    const start = new Date(trip.start_date)\n    const end = new Date(trip.end_date)\n    const diffTime = Math.abs(end.getTime() - start.getTime())\n    return Math.ceil(diffTime / (1000 * 60 * 60 * 24))\n  }\n\n  const budgetProgress = trip?.budget_total \n    ? Math.min((trip.budget_spent / trip.budget_total) * 100, 100)\n    : 0\n\n  if (loading) {\n    return <Loading size=\"lg\" text=\"Loading trip details...\" className=\"h-64\" />\n  }\n\n  if (error || !trip) {\n    return (\n      <div className=\"text-center py-12\">\n        <div className=\"bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto\">\n          <h3 className=\"text-lg font-medium text-red-800 mb-2\">Trip Not Found</h3>\n          <p className=\"text-red-600 mb-4\">{error || 'The trip you are looking for does not exist.'}</p>\n          <Link href=\"/trips\">\n            <Button variant=\"outline\" className=\"text-red-600 border-red-300 hover:bg-red-50\">\n              <ArrowLeftIcon className=\"h-4 w-4 mr-2\" />\n              Back to Trips\n            </Button>\n          </Link>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <motion.div\n        initial={{ opacity: 0, y: -20 }}\n        animate={{ opacity: 1, y: 0 }}\n        className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\"\n      >\n        <div className=\"flex items-center space-x-4\">\n          <Link href=\"/trips\">\n            <Button variant=\"outline\" size=\"sm\">\n              <ArrowLeftIcon className=\"h-4 w-4 mr-2\" />\n              Back to Trips\n            </Button>\n          </Link>\n          <div>\n            <h1 className=\"text-3xl font-bold text-gray-900\">{trip.title}</h1>\n            <div className=\"flex items-center space-x-3 mt-2\">\n              <Badge className={getStatusColor(trip.status)}>\n                {trip.status.replace('_', ' ').toUpperCase()}\n              </Badge>\n              <Badge className={getTripTypeColor(trip.trip_type)}>\n                {trip.trip_type}\n              </Badge>\n              {trip.is_shared && (\n                <Badge className=\"bg-blue-100 text-blue-800\">\n                  <ShareIcon className=\"h-3 w-3 mr-1\" />\n                  Shared\n                </Badge>\n              )}\n            </div>\n          </div>\n        </div>\n        \n        <div className=\"flex items-center space-x-3\">\n          <Button variant=\"outline\">\n            <EditIcon className=\"h-4 w-4 mr-2\" />\n            Edit Trip\n          </Button>\n          <Button variant=\"outline\">\n            <ShareIcon className=\"h-4 w-4 mr-2\" />\n            Share\n          </Button>\n        </div>\n      </motion.div>\n\n      {/* Cover Image */}\n      {trip.cover_image_url && (\n        <motion.div\n          initial={{ opacity: 0, scale: 0.95 }}\n          animate={{ opacity: 1, scale: 1 }}\n          className=\"relative h-64 rounded-lg overflow-hidden\"\n        >\n          <img \n            src={trip.cover_image_url} \n            alt={trip.title}\n            className=\"w-full h-full object-cover\"\n          />\n          <div className=\"absolute inset-0 bg-black bg-opacity-20\" />\n        </motion.div>\n      )}\n\n      {/* Trip Overview */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ delay: 0.1 }}\n        className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\"\n      >\n        {/* Destination */}\n        <div className=\"bg-white p-6 rounded-lg shadow-sm border border-gray-200\">\n          <div className=\"flex items-center mb-3\">\n            <div className=\"bg-blue-100 p-2 rounded-lg\">\n              <MapPinIcon className=\"h-5 w-5 text-blue-600\" />\n            </div>\n            <h3 className=\"ml-3 text-sm font-medium text-gray-600\">Destination</h3>\n          </div>\n          <p className=\"text-lg font-semibold text-gray-900\">\n            {trip.destination_city ? `${trip.destination_city}, ` : ''}{trip.destination_country}\n          </p>\n        </div>\n\n        {/* Duration */}\n        <div className=\"bg-white p-6 rounded-lg shadow-sm border border-gray-200\">\n          <div className=\"flex items-center mb-3\">\n            <div className=\"bg-green-100 p-2 rounded-lg\">\n              <ClockIcon className=\"h-5 w-5 text-green-600\" />\n            </div>\n            <h3 className=\"ml-3 text-sm font-medium text-gray-600\">Duration</h3>\n          </div>\n          <p className=\"text-lg font-semibold text-gray-900\">{calculateDuration()} days</p>\n          <p className=\"text-sm text-gray-500 mt-1\">\n            {formatDate(trip.start_date)} - {formatDate(trip.end_date)}\n          </p>\n        </div>\n\n        {/* Travelers */}\n        <div className=\"bg-white p-6 rounded-lg shadow-sm border border-gray-200\">\n          <div className=\"flex items-center mb-3\">\n            <div className=\"bg-purple-100 p-2 rounded-lg\">\n              <UsersIcon className=\"h-5 w-5 text-purple-600\" />\n            </div>\n            <h3 className=\"ml-3 text-sm font-medium text-gray-600\">Travelers</h3>\n          </div>\n          <p className=\"text-lg font-semibold text-gray-900\">{trip.traveler_count}</p>\n          <p className=\"text-sm text-gray-500 mt-1\">\n            {trip.traveler_count === 1 ? 'Solo trip' : 'Group trip'}\n          </p>\n        </div>\n\n        {/* Budget */}\n        <div className=\"bg-white p-6 rounded-lg shadow-sm border border-gray-200\">\n          <div className=\"flex items-center mb-3\">\n            <div className=\"bg-yellow-100 p-2 rounded-lg\">\n              <DollarSignIcon className=\"h-5 w-5 text-yellow-600\" />\n            </div>\n            <h3 className=\"ml-3 text-sm font-medium text-gray-600\">Budget</h3>\n          </div>\n          {trip.budget_total ? (\n            <>\n              <p className=\"text-lg font-semibold text-gray-900\">\n                {new Intl.NumberFormat('en-US', {\n                  style: 'currency',\n                  currency: trip.currency,\n                }).format(trip.budget_spent)} / {new Intl.NumberFormat('en-US', {\n                  style: 'currency',\n                  currency: trip.currency,\n                }).format(trip.budget_total)}\n              </p>\n              <div className=\"w-full bg-gray-200 rounded-full h-2 mt-2\">\n                <div \n                  className={`h-2 rounded-full ${\n                    budgetProgress > 90 ? 'bg-red-500' : \n                    budgetProgress > 75 ? 'bg-yellow-500' : 'bg-green-500'\n                  }`}\n                  style={{ width: `${budgetProgress}%` }}\n                />\n              </div>\n              <p className=\"text-sm text-gray-500 mt-1\">{budgetProgress.toFixed(1)}% used</p>\n            </>\n          ) : (\n            <p className=\"text-lg font-semibold text-gray-900\">No budget set</p>\n          )}\n        </div>\n      </motion.div>\n\n      {/* Description */}\n      {trip.description && (\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.2 }}\n          className=\"bg-white p-6 rounded-lg shadow-sm border border-gray-200\"\n        >\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-3\">Description</h3>\n          <p className=\"text-gray-600 leading-relaxed\">{trip.description}</p>\n        </motion.div>\n      )}\n\n      {/* Trip Map */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ delay: 0.3 }}\n        className=\"bg-white p-6 rounded-lg shadow-sm border border-gray-200\"\n      >\n        <TripMap\n          tripId={tripId}\n          destination={{\n            city: trip.destination_city || undefined,\n            country: trip.destination_country,\n            // TODO: Add latitude/longitude to trip data\n          }}\n          pins={tripPins}\n          onPinsChange={setTripPins}\n        />\n      </motion.div>\n\n      {/* Quick Actions */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ delay: 0.4 }}\n        className=\"grid grid-cols-1 md:grid-cols-3 gap-4\"\n      >\n        <Button className=\"h-16 flex flex-col items-center justify-center space-y-1\">\n          <MapPinIcon className=\"h-5 w-5\" />\n          <span className=\"text-sm\">Add Destination</span>\n        </Button>\n        \n        <Button variant=\"outline\" className=\"h-16 flex flex-col items-center justify-center space-y-1\">\n          <CalendarIcon className=\"h-5 w-5\" />\n          <span className=\"text-sm\">Add Activity</span>\n        </Button>\n        \n        <Button variant=\"outline\" className=\"h-16 flex flex-col items-center justify-center space-y-1\">\n          <DollarSignIcon className=\"h-5 w-5\" />\n          <span className=\"text-sm\">Add Expense</span>\n        </Button>\n      </motion.div>\n\n      {/* Coming Soon Sections */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ delay: 0.4 }}\n        className=\"bg-gray-50 border-2 border-dashed border-gray-300 rounded-lg p-8 text-center\"\n      >\n        <TrendingUpIcon className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n        <h3 className=\"text-lg font-medium text-gray-900 mb-2\">More Features Coming Soon!</h3>\n        <p className=\"text-gray-600 mb-4\">\n          Destinations, activities, expenses, itinerary planning, and more advanced features are being developed.\n        </p>\n        <div className=\"flex flex-wrap justify-center gap-2\">\n          <Badge variant=\"secondary\">Destinations</Badge>\n          <Badge variant=\"secondary\">Activities</Badge>\n          <Badge variant=\"secondary\">Expenses</Badge>\n          <Badge variant=\"secondary\">Itinerary</Badge>\n          <Badge variant=\"secondary\">Maps</Badge>\n          <Badge variant=\"secondary\">Weather</Badge>\n        </div>\n      </motion.div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AACA;AAvBA;;;;;;;;;;;;;AAyBe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,OAAO,EAAE;IAExB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAS,EAAE,EAAE,2BAA2B;;IAE/E,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ;YACV;QACF;IACF,GAAG;QAAC;KAAO;IAEX,MAAM,YAAY;QAChB,IAAI;YACF,WAAW;YACX,MAAM,OAAO,MAAM,oJAAA,CAAA,qBAAkB,CAAC,OAAO,CAAC;YAC9C,QAAQ;QACV,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,SAAS;YACT,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAe,OAAO;YAC3B,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAa,OAAO;YACzB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAS,OAAO;YACrB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,SAAS;YACT,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,MAAM,oBAAoB;QACxB,IAAI,CAAC,MAAM,OAAO;QAClB,MAAM,QAAQ,IAAI,KAAK,KAAK,UAAU;QACtC,MAAM,MAAM,IAAI,KAAK,KAAK,QAAQ;QAClC,MAAM,WAAW,KAAK,GAAG,CAAC,IAAI,OAAO,KAAK,MAAM,OAAO;QACvD,OAAO,KAAK,IAAI,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;IAClD;IAEA,MAAM,iBAAiB,MAAM,eACzB,KAAK,GAAG,CAAC,AAAC,KAAK,YAAY,GAAG,KAAK,YAAY,GAAI,KAAK,OACxD;IAEJ,IAAI,SAAS;QACX,qBAAO,8OAAC,mIAAA,CAAA,UAAO;YAAC,MAAK;YAAK,MAAK;YAA0B,WAAU;;;;;;IACrE;IAEA,IAAI,SAAS,CAAC,MAAM;QAClB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,8OAAC;wBAAE,WAAU;kCAAqB,SAAS;;;;;;kCAC3C,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;kCACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,WAAU;;8CAClC,8OAAC,oNAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;;;;;;IAOtD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG,CAAC;gBAAG;gBAC9B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,WAAU;;kCAEV,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,MAAK;;sDAC7B,8OAAC,oNAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;0CAI9C,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAoC,KAAK,KAAK;;;;;;kDAC5D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,WAAW,eAAe,KAAK,MAAM;0DACzC,KAAK,MAAM,CAAC,OAAO,CAAC,KAAK,KAAK,WAAW;;;;;;0DAE5C,8OAAC,iIAAA,CAAA,QAAK;gDAAC,WAAW,iBAAiB,KAAK,SAAS;0DAC9C,KAAK,SAAS;;;;;;4CAEhB,KAAK,SAAS,kBACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,WAAU;;kEACf,8OAAC,wMAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;kCAQhD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;;kDACd,8OAAC,+MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGvC,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;;kDACd,8OAAC,wMAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;YAO3C,KAAK,eAAe,kBACnB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,OAAO;gBAAK;gBACnC,SAAS;oBAAE,SAAS;oBAAG,OAAO;gBAAE;gBAChC,WAAU;;kCAEV,8OAAC;wBACC,KAAK,KAAK,eAAe;wBACzB,KAAK,KAAK,KAAK;wBACf,WAAU;;;;;;kCAEZ,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAKnB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,OAAO;gBAAI;gBACzB,WAAU;;kCAGV,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,8MAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;kDAExB,8OAAC;wCAAG,WAAU;kDAAyC;;;;;;;;;;;;0CAEzD,8OAAC;gCAAE,WAAU;;oCACV,KAAK,gBAAgB,GAAG,GAAG,KAAK,gBAAgB,CAAC,EAAE,CAAC,GAAG;oCAAI,KAAK,mBAAmB;;;;;;;;;;;;;kCAKxF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,wMAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;kDAEvB,8OAAC;wCAAG,WAAU;kDAAyC;;;;;;;;;;;;0CAEzD,8OAAC;gCAAE,WAAU;;oCAAuC;oCAAoB;;;;;;;0CACxE,8OAAC;gCAAE,WAAU;;oCACV,WAAW,KAAK,UAAU;oCAAE;oCAAI,WAAW,KAAK,QAAQ;;;;;;;;;;;;;kCAK7D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,wMAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;kDAEvB,8OAAC;wCAAG,WAAU;kDAAyC;;;;;;;;;;;;0CAEzD,8OAAC;gCAAE,WAAU;0CAAuC,KAAK,cAAc;;;;;;0CACvE,8OAAC;gCAAE,WAAU;0CACV,KAAK,cAAc,KAAK,IAAI,cAAc;;;;;;;;;;;;kCAK/C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,sNAAA,CAAA,iBAAc;4CAAC,WAAU;;;;;;;;;;;kDAE5B,8OAAC;wCAAG,WAAU;kDAAyC;;;;;;;;;;;;4BAExD,KAAK,YAAY,iBAChB;;kDACE,8OAAC;wCAAE,WAAU;;4CACV,IAAI,KAAK,YAAY,CAAC,SAAS;gDAC9B,OAAO;gDACP,UAAU,KAAK,QAAQ;4CACzB,GAAG,MAAM,CAAC,KAAK,YAAY;4CAAE;4CAAI,IAAI,KAAK,YAAY,CAAC,SAAS;gDAC9D,OAAO;gDACP,UAAU,KAAK,QAAQ;4CACzB,GAAG,MAAM,CAAC,KAAK,YAAY;;;;;;;kDAE7B,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,WAAW,CAAC,iBAAiB,EAC3B,iBAAiB,KAAK,eACtB,iBAAiB,KAAK,kBAAkB,gBACxC;4CACF,OAAO;gDAAE,OAAO,GAAG,eAAe,CAAC,CAAC;4CAAC;;;;;;;;;;;kDAGzC,8OAAC;wCAAE,WAAU;;4CAA8B,eAAe,OAAO,CAAC;4CAAG;;;;;;;;6DAGvE,8OAAC;gCAAE,WAAU;0CAAsC;;;;;;;;;;;;;;;;;;YAMxD,KAAK,WAAW,kBACf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,OAAO;gBAAI;gBACzB,WAAU;;kCAEV,8OAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,8OAAC;wBAAE,WAAU;kCAAiC,KAAK,WAAW;;;;;;;;;;;;0BAKlE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,OAAO;gBAAI;gBACzB,WAAU;0BAEV,cAAA,8OAAC,gJAAA,CAAA,UAAO;oBACN,QAAQ;oBACR,aAAa;wBACX,MAAM,KAAK,gBAAgB,IAAI;wBAC/B,SAAS,KAAK,mBAAmB;oBAEnC;oBACA,MAAM;oBACN,cAAc;;;;;;;;;;;0BAKlB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,OAAO;gBAAI;gBACzB,WAAU;;kCAEV,8OAAC,kIAAA,CAAA,SAAM;wBAAC,WAAU;;0CAChB,8OAAC,8MAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;0CACtB,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;kCAG5B,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAU,WAAU;;0CAClC,8OAAC,8MAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;0CACxB,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;kCAG5B,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAU,WAAU;;0CAClC,8OAAC,sNAAA,CAAA,iBAAc;gCAAC,WAAU;;;;;;0CAC1B,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;0BAK9B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,OAAO;gBAAI;gBACzB,WAAU;;kCAEV,8OAAC,sNAAA,CAAA,iBAAc;wBAAC,WAAU;;;;;;kCAC1B,8OAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAGlC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAY;;;;;;0CAC3B,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAY;;;;;;0CAC3B,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAY;;;;;;0CAC3B,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAY;;;;;;0CAC3B,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAY;;;;;;0CAC3B,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAY;;;;;;;;;;;;;;;;;;;;;;;;AAKrC", "debugId": null}}]}