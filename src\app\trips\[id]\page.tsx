'use client'

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import { motion } from 'framer-motion'
import { tripPlannerService, type Trip } from '@/lib/services/trip-planner-service'
import { Button } from '@/components/ui/Button'
import { Loading } from '@/components/ui/Loading'
import { Badge } from '@/components/ui/Badge'
import TripMap from '@/components/trip-planner/TripMap'
import { 
  MapPinIcon, 
  CalendarIcon, 
  DollarSignIcon, 
  UsersIcon,
  ArrowLeftIcon,
  EditIcon,
  ShareIcon,
  PlusIcon,
  ClockIcon,
  TrendingUpIcon
} from 'lucide-react'
import Link from 'next/link'
import toast from 'react-hot-toast'

export default function TripDetailPage() {
  const params = useParams()
  const tripId = params.id as string
  
  const [trip, setTrip] = useState<Trip | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [tripPins, setTripPins] = useState<any[]>([]) // TODO: Define proper type

  useEffect(() => {
    if (tripId) {
      fetchTrip()
    }
  }, [tripId])

  const fetchTrip = async () => {
    try {
      setLoading(true)
      const data = await tripPlannerService.getTrip(tripId)
      setTrip(data)
    } catch (error) {
      console.error('Error fetching trip:', error)
      setError('Failed to load trip details')
      toast.error('Failed to load trip details')
    } finally {
      setLoading(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'planning': return 'bg-blue-100 text-blue-800'
      case 'booked': return 'bg-green-100 text-green-800'
      case 'in_progress': return 'bg-yellow-100 text-yellow-800'
      case 'completed': return 'bg-gray-100 text-gray-800'
      case 'cancelled': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getTripTypeColor = (type: string) => {
    switch (type) {
      case 'leisure': return 'bg-purple-100 text-purple-800'
      case 'business': return 'bg-blue-100 text-blue-800'
      case 'adventure': return 'bg-orange-100 text-orange-800'
      case 'family': return 'bg-green-100 text-green-800'
      case 'romantic': return 'bg-pink-100 text-pink-800'
      case 'solo': return 'bg-indigo-100 text-indigo-800'
      case 'group': return 'bg-yellow-100 text-yellow-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const calculateDuration = () => {
    if (!trip) return 0
    const start = new Date(trip.start_date)
    const end = new Date(trip.end_date)
    const diffTime = Math.abs(end.getTime() - start.getTime())
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  }

  const budgetProgress = trip?.budget_total 
    ? Math.min((trip.budget_spent / trip.budget_total) * 100, 100)
    : 0

  if (loading) {
    return <Loading size="lg" text="Loading trip details..." className="h-64" />
  }

  if (error || !trip) {
    return (
      <div className="text-center py-12">
        <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto">
          <h3 className="text-lg font-medium text-red-800 mb-2">Trip Not Found</h3>
          <p className="text-red-600 mb-4">{error || 'The trip you are looking for does not exist.'}</p>
          <Link href="/trips">
            <Button variant="outline" className="text-red-600 border-red-300 hover:bg-red-50">
              <ArrowLeftIcon className="h-4 w-4 mr-2" />
              Back to Trips
            </Button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4"
      >
        <div className="flex items-center space-x-4">
          <Link href="/trips">
            <Button variant="outline" size="sm">
              <ArrowLeftIcon className="h-4 w-4 mr-2" />
              Back to Trips
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{trip.title}</h1>
            <div className="flex items-center space-x-3 mt-2">
              <Badge className={getStatusColor(trip.status)}>
                {trip.status.replace('_', ' ').toUpperCase()}
              </Badge>
              <Badge className={getTripTypeColor(trip.trip_type)}>
                {trip.trip_type}
              </Badge>
              {trip.is_shared && (
                <Badge className="bg-blue-100 text-blue-800">
                  <ShareIcon className="h-3 w-3 mr-1" />
                  Shared
                </Badge>
              )}
            </div>
          </div>
        </div>
        
        <div className="flex items-center space-x-3">
          <Button variant="outline">
            <EditIcon className="h-4 w-4 mr-2" />
            Edit Trip
          </Button>
          <Button variant="outline">
            <ShareIcon className="h-4 w-4 mr-2" />
            Share
          </Button>
        </div>
      </motion.div>

      {/* Cover Image */}
      {trip.cover_image_url && (
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          className="relative h-64 rounded-lg overflow-hidden"
        >
          <img 
            src={trip.cover_image_url} 
            alt={trip.title}
            className="w-full h-full object-cover"
          />
          <div className="absolute inset-0 bg-black bg-opacity-20" />
        </motion.div>
      )}

      {/* Trip Overview */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
      >
        {/* Destination */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center mb-3">
            <div className="bg-blue-100 p-2 rounded-lg">
              <MapPinIcon className="h-5 w-5 text-blue-600" />
            </div>
            <h3 className="ml-3 text-sm font-medium text-gray-600">Destination</h3>
          </div>
          <p className="text-lg font-semibold text-gray-900">
            {trip.destination_city ? `${trip.destination_city}, ` : ''}{trip.destination_country}
          </p>
        </div>

        {/* Duration */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center mb-3">
            <div className="bg-green-100 p-2 rounded-lg">
              <ClockIcon className="h-5 w-5 text-green-600" />
            </div>
            <h3 className="ml-3 text-sm font-medium text-gray-600">Duration</h3>
          </div>
          <p className="text-lg font-semibold text-gray-900">{calculateDuration()} days</p>
          <p className="text-sm text-gray-500 mt-1">
            {formatDate(trip.start_date)} - {formatDate(trip.end_date)}
          </p>
        </div>

        {/* Travelers */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center mb-3">
            <div className="bg-purple-100 p-2 rounded-lg">
              <UsersIcon className="h-5 w-5 text-purple-600" />
            </div>
            <h3 className="ml-3 text-sm font-medium text-gray-600">Travelers</h3>
          </div>
          <p className="text-lg font-semibold text-gray-900">{trip.traveler_count}</p>
          <p className="text-sm text-gray-500 mt-1">
            {trip.traveler_count === 1 ? 'Solo trip' : 'Group trip'}
          </p>
        </div>

        {/* Budget */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center mb-3">
            <div className="bg-yellow-100 p-2 rounded-lg">
              <DollarSignIcon className="h-5 w-5 text-yellow-600" />
            </div>
            <h3 className="ml-3 text-sm font-medium text-gray-600">Budget</h3>
          </div>
          {trip.budget_total ? (
            <>
              <p className="text-lg font-semibold text-gray-900">
                {new Intl.NumberFormat('en-US', {
                  style: 'currency',
                  currency: trip.currency,
                }).format(trip.budget_spent)} / {new Intl.NumberFormat('en-US', {
                  style: 'currency',
                  currency: trip.currency,
                }).format(trip.budget_total)}
              </p>
              <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                <div 
                  className={`h-2 rounded-full ${
                    budgetProgress > 90 ? 'bg-red-500' : 
                    budgetProgress > 75 ? 'bg-yellow-500' : 'bg-green-500'
                  }`}
                  style={{ width: `${budgetProgress}%` }}
                />
              </div>
              <p className="text-sm text-gray-500 mt-1">{budgetProgress.toFixed(1)}% used</p>
            </>
          ) : (
            <p className="text-lg font-semibold text-gray-900">No budget set</p>
          )}
        </div>
      </motion.div>

      {/* Description */}
      {trip.description && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-white p-6 rounded-lg shadow-sm border border-gray-200"
        >
          <h3 className="text-lg font-semibold text-gray-900 mb-3">Description</h3>
          <p className="text-gray-600 leading-relaxed">{trip.description}</p>
        </motion.div>
      )}

      {/* Trip Map */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
        className="bg-white p-6 rounded-lg shadow-sm border border-gray-200"
      >
        <TripMap
          tripId={tripId}
          destination={{
            city: trip.destination_city || undefined,
            country: trip.destination_country,
            // TODO: Add latitude/longitude to trip data
          }}
          pins={tripPins}
          onPinsChange={setTripPins}
        />
      </motion.div>

      {/* Quick Actions */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
        className="grid grid-cols-1 md:grid-cols-3 gap-4"
      >
        <Button className="h-16 flex flex-col items-center justify-center space-y-1">
          <MapPinIcon className="h-5 w-5" />
          <span className="text-sm">Add Destination</span>
        </Button>
        
        <Button variant="outline" className="h-16 flex flex-col items-center justify-center space-y-1">
          <CalendarIcon className="h-5 w-5" />
          <span className="text-sm">Add Activity</span>
        </Button>
        
        <Button variant="outline" className="h-16 flex flex-col items-center justify-center space-y-1">
          <DollarSignIcon className="h-5 w-5" />
          <span className="text-sm">Add Expense</span>
        </Button>
      </motion.div>

      {/* Coming Soon Sections */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
        className="bg-gray-50 border-2 border-dashed border-gray-300 rounded-lg p-8 text-center"
      >
        <TrendingUpIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">More Features Coming Soon!</h3>
        <p className="text-gray-600 mb-4">
          Destinations, activities, expenses, itinerary planning, and more advanced features are being developed.
        </p>
        <div className="flex flex-wrap justify-center gap-2">
          <Badge variant="secondary">Destinations</Badge>
          <Badge variant="secondary">Activities</Badge>
          <Badge variant="secondary">Expenses</Badge>
          <Badge variant="secondary">Itinerary</Badge>
          <Badge variant="secondary">Maps</Badge>
          <Badge variant="secondary">Weather</Badge>
        </div>
      </motion.div>
    </div>
  )
}
