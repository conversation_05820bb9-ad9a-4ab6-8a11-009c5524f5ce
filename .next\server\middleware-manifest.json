{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_f58e8655._.js", "server/edge/chunks/[root-of-the-server]__c6c91fdf._.js", "server/edge/chunks/edge-wrapper_c6630286.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "3bMorcNST1nn7TlHZNKY9DXdKc+0VsLLH8M8jnlcKQ0=", "__NEXT_PREVIEW_MODE_ID": "e699980c6df521f4fd57506a2356c934", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "57e65ab6f5e678fd3dc97099ede0f852ec2d61cfaeb9cba1858bd3a15ab63cce", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "4a09c02d045ce609476bae9a4b53e20d604b839703bf097ad405198607c97599"}}}, "sortedMiddleware": ["/"], "functions": {}}