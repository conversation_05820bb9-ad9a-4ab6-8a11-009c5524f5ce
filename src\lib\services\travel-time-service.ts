export interface TravelTimeRequest {
  origin: {
    latitude: number
    longitude: number
    name?: string
  }
  destination: {
    latitude: number
    longitude: number
    name?: string
  }
  travelMode: 'DRIVING' | 'WALKING' | 'TRANSIT' | 'BICYCLING'
  departureTime?: Date
  arrivalTime?: Date
}

export interface TravelTimeResult {
  distance: {
    text: string
    value: number // meters
  }
  duration: {
    text: string
    value: number // seconds
  }
  durationInTraffic?: {
    text: string
    value: number // seconds
  }
  status: string
  travelMode: string
}

export interface TravelMatrix {
  origins: Array<{ latitude: number; longitude: number; name?: string }>
  destinations: Array<{ latitude: number; longitude: number; name?: string }>
  results: TravelTimeResult[][]
  travelMode: string
}

class TravelTimeService {
  private distanceMatrixService: google.maps.DistanceMatrixService | null = null
  private directionsService: google.maps.DirectionsService | null = null

  constructor() {
    if (typeof google !== 'undefined' && google.maps) {
      this.distanceMatrixService = new google.maps.DistanceMatrixService()
      this.directionsService = new google.maps.DirectionsService()
    }
  }

  async calculateTravelTime(request: TravelTimeRequest): Promise<TravelTimeResult> {
    if (!this.distanceMatrixService) {
      throw new Error('Google Maps Distance Matrix service not initialized')
    }

    return new Promise((resolve, reject) => {
      const origins = [new google.maps.LatLng(request.origin.latitude, request.origin.longitude)]
      const destinations = [new google.maps.LatLng(request.destination.latitude, request.destination.longitude)]

      const matrixRequest: google.maps.DistanceMatrixRequest = {
        origins,
        destinations,
        travelMode: google.maps.TravelMode[request.travelMode],
        unitSystem: google.maps.UnitSystem.METRIC,
        avoidHighways: false,
        avoidTolls: false
      }

      // Add departure time for driving mode to get traffic data
      if (request.travelMode === 'DRIVING' && request.departureTime) {
        matrixRequest.drivingOptions = {
          departureTime: request.departureTime,
          trafficModel: google.maps.TrafficModel.BEST_GUESS
        }
      }

      // Add transit options for public transport
      if (request.travelMode === 'TRANSIT') {
        matrixRequest.transitOptions = {
          departureTime: request.departureTime,
          modes: [google.maps.TransitMode.BUS, google.maps.TransitMode.RAIL, google.maps.TransitMode.SUBWAY],
          routingPreference: google.maps.TransitRoutePreference.BEST_GUESS
        }
      }

      this.distanceMatrixService.getDistanceMatrix(matrixRequest, (response, status) => {
        if (status === google.maps.DistanceMatrixStatus.OK && response) {
          const element = response.rows[0].elements[0]
          
          if (element.status === google.maps.DistanceMatrixElementStatus.OK) {
            const result: TravelTimeResult = {
              distance: element.distance!,
              duration: element.duration!,
              status: element.status,
              travelMode: request.travelMode
            }

            // Add traffic duration if available
            if (element.duration_in_traffic) {
              result.durationInTraffic = element.duration_in_traffic
            }

            resolve(result)
          } else {
            reject(new Error(`Travel time calculation failed: ${element.status}`))
          }
        } else {
          reject(new Error(`Distance Matrix API failed: ${status}`))
        }
      })
    })
  }

  async calculateTravelMatrix(
    origins: Array<{ latitude: number; longitude: number; name?: string }>,
    destinations: Array<{ latitude: number; longitude: number; name?: string }>,
    travelMode: 'DRIVING' | 'WALKING' | 'TRANSIT' | 'BICYCLING' = 'DRIVING',
    departureTime?: Date
  ): Promise<TravelMatrix> {
    if (!this.distanceMatrixService) {
      throw new Error('Google Maps Distance Matrix service not initialized')
    }

    return new Promise((resolve, reject) => {
      const googleOrigins = origins.map(o => new google.maps.LatLng(o.latitude, o.longitude))
      const googleDestinations = destinations.map(d => new google.maps.LatLng(d.latitude, d.longitude))

      const matrixRequest: google.maps.DistanceMatrixRequest = {
        origins: googleOrigins,
        destinations: googleDestinations,
        travelMode: google.maps.TravelMode[travelMode],
        unitSystem: google.maps.UnitSystem.METRIC,
        avoidHighways: false,
        avoidTolls: false
      }

      if (travelMode === 'DRIVING' && departureTime) {
        matrixRequest.drivingOptions = {
          departureTime,
          trafficModel: google.maps.TrafficModel.BEST_GUESS
        }
      }

      if (travelMode === 'TRANSIT') {
        matrixRequest.transitOptions = {
          departureTime: departureTime || new Date(),
          modes: [google.maps.TransitMode.BUS, google.maps.TransitMode.RAIL, google.maps.TransitMode.SUBWAY],
          routingPreference: google.maps.TransitRoutePreference.BEST_GUESS
        }
      }

      this.distanceMatrixService.getDistanceMatrix(matrixRequest, (response, status) => {
        if (status === google.maps.DistanceMatrixStatus.OK && response) {
          const results: TravelTimeResult[][] = []

          response.rows.forEach((row, originIndex) => {
            results[originIndex] = []
            row.elements.forEach((element, destIndex) => {
              if (element.status === google.maps.DistanceMatrixElementStatus.OK) {
                const result: TravelTimeResult = {
                  distance: element.distance!,
                  duration: element.duration!,
                  status: element.status,
                  travelMode
                }

                if (element.duration_in_traffic) {
                  result.durationInTraffic = element.duration_in_traffic
                }

                results[originIndex][destIndex] = result
              } else {
                results[originIndex][destIndex] = {
                  distance: { text: 'N/A', value: 0 },
                  duration: { text: 'N/A', value: 0 },
                  status: element.status,
                  travelMode
                }
              }
            })
          })

          resolve({
            origins,
            destinations,
            results,
            travelMode
          })
        } else {
          reject(new Error(`Distance Matrix API failed: ${status}`))
        }
      })
    })
  }

  async getDetailedDirections(
    origin: { latitude: number; longitude: number },
    destination: { latitude: number; longitude: number },
    travelMode: 'DRIVING' | 'WALKING' | 'TRANSIT' | 'BICYCLING' = 'DRIVING',
    waypoints?: Array<{ latitude: number; longitude: number }>,
    departureTime?: Date
  ): Promise<google.maps.DirectionsResult> {
    if (!this.directionsService) {
      throw new Error('Google Maps Directions service not initialized')
    }

    return new Promise((resolve, reject) => {
      const request: google.maps.DirectionsRequest = {
        origin: new google.maps.LatLng(origin.latitude, origin.longitude),
        destination: new google.maps.LatLng(destination.latitude, destination.longitude),
        travelMode: google.maps.TravelMode[travelMode],
        unitSystem: google.maps.UnitSystem.METRIC
      }

      if (waypoints && waypoints.length > 0) {
        request.waypoints = waypoints.map(wp => ({
          location: new google.maps.LatLng(wp.latitude, wp.longitude),
          stopover: true
        }))
      }

      if (travelMode === 'DRIVING' && departureTime) {
        request.drivingOptions = {
          departureTime,
          trafficModel: google.maps.TrafficModel.BEST_GUESS
        }
      }

      if (travelMode === 'TRANSIT') {
        request.transitOptions = {
          departureTime: departureTime || new Date(),
          modes: [google.maps.TransitMode.BUS, google.maps.TransitMode.RAIL, google.maps.TransitMode.SUBWAY],
          routingPreference: google.maps.TransitRoutePreference.BEST_GUESS
        }
      }

      this.directionsService.route(request, (result, status) => {
        if (status === google.maps.DirectionsStatus.OK && result) {
          resolve(result)
        } else {
          reject(new Error(`Directions request failed: ${status}`))
        }
      })
    })
  }

  formatDuration(seconds: number, includeTraffic: boolean = false): string {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)

    if (hours > 0) {
      return `${hours}h ${minutes}m`
    }
    return `${minutes}m`
  }

  formatDistance(meters: number): string {
    const km = meters / 1000
    if (km >= 1) {
      return `${Math.round(km * 10) / 10} km`
    }
    return `${Math.round(meters)} m`
  }

  getTravelModeIcon(mode: string): string {
    switch (mode) {
      case 'DRIVING': return '🚗'
      case 'WALKING': return '🚶'
      case 'TRANSIT': return '🚌'
      case 'BICYCLING': return '🚴'
      default: return '🚗'
    }
  }

  estimateArrivalTime(departureTime: Date, durationSeconds: number): Date {
    return new Date(departureTime.getTime() + durationSeconds * 1000)
  }
}

export const travelTimeService = new TravelTimeService()
