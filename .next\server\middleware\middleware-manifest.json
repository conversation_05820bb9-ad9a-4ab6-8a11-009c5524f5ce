{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_f58e8655._.js", "server/edge/chunks/[root-of-the-server]__c6c91fdf._.js", "server/edge/chunks/edge-wrapper_c6630286.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "3bMorcNST1nn7TlHZNKY9DXdKc+0VsLLH8M8jnlcKQ0=", "__NEXT_PREVIEW_MODE_ID": "f39375c81da554c0c2388241427ab20c", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "8e410647cad95f007d8db235e4f99187ea6dcba1f1b655448ae4c06ff586ed8f", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "262bbabadeeb9dda2ee11f23f02f4c65957ba9c6d93eef73ec31e2bf18a024f2"}}}, "instrumentation": null, "functions": {}}