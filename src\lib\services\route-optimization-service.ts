export interface RouteWaypoint {
  id: string
  name: string
  latitude: number
  longitude: number
  category: 'accommodation' | 'restaurant' | 'attraction' | 'transport' | 'shopping' | 'other'
  priority: 'high' | 'medium' | 'low'
  estimatedDuration?: number // minutes to spend at location
}

export interface RouteSegment {
  from: RouteWaypoint
  to: RouteWaypoint
  distance: number // meters
  duration: number // seconds
  polyline: string
  instructions: string[]
}

export interface OptimizedRoute {
  waypoints: RouteWaypoint[]
  segments: RouteSegment[]
  totalDistance: number // meters
  totalDuration: number // seconds (travel time only)
  totalTimeWithStops: number // seconds (including estimated stop times)
  estimatedCost: {
    fuel: number
    tolls: number
    parking: number
  }
}

export interface RouteOptimizationOptions {
  startLocation?: { latitude: number; longitude: number }
  endLocation?: { latitude: number; longitude: number }
  travelMode: 'DRIVING' | 'WALKING' | 'TRANSIT' | 'BICYCLING'
  avoidTolls?: boolean
  avoidHighways?: boolean
  optimizeOrder?: boolean
  maxWaypoints?: number
}

class RouteOptimizationService {
  private directionsService: google.maps.DirectionsService | null = null
  private distanceMatrixService: google.maps.DistanceMatrixService | null = null

  constructor() {
    if (typeof google !== 'undefined' && google.maps) {
      this.directionsService = new google.maps.DirectionsService()
      this.distanceMatrixService = new google.maps.DistanceMatrixService()
    }
  }

  async optimizeRoute(
    waypoints: RouteWaypoint[],
    options: RouteOptimizationOptions = { travelMode: 'DRIVING' }
  ): Promise<OptimizedRoute> {
    if (!this.directionsService || !this.distanceMatrixService) {
      throw new Error('Google Maps services not initialized')
    }

    if (waypoints.length < 2) {
      throw new Error('At least 2 waypoints are required for route optimization')
    }

    let optimizedWaypoints = waypoints

    // If optimization is requested and we have more than 2 waypoints
    if (options.optimizeOrder && waypoints.length > 2) {
      optimizedWaypoints = await this.optimizeWaypointOrder(waypoints, options)
    }

    // Calculate route segments
    const segments = await this.calculateRouteSegments(optimizedWaypoints, options)

    // Calculate totals
    const totalDistance = segments.reduce((sum, segment) => sum + segment.distance, 0)
    const totalDuration = segments.reduce((sum, segment) => sum + segment.duration, 0)
    const totalTimeWithStops = totalDuration + 
      optimizedWaypoints.reduce((sum, wp) => sum + (wp.estimatedDuration || 0) * 60, 0)

    // Estimate costs
    const estimatedCost = this.estimateTravelCosts(totalDistance, segments.length, options)

    return {
      waypoints: optimizedWaypoints,
      segments,
      totalDistance,
      totalDuration,
      totalTimeWithStops,
      estimatedCost
    }
  }

  private async optimizeWaypointOrder(
    waypoints: RouteWaypoint[],
    options: RouteOptimizationOptions
  ): Promise<RouteWaypoint[]> {
    // Use Google's distance matrix to find optimal order
    const origins = waypoints.map(wp => new google.maps.LatLng(wp.latitude, wp.longitude))
    const destinations = origins

    return new Promise((resolve, reject) => {
      this.distanceMatrixService!.getDistanceMatrix({
        origins,
        destinations,
        travelMode: google.maps.TravelMode[options.travelMode],
        avoidHighways: options.avoidHighways || false,
        avoidTolls: options.avoidTolls || false
      }, (response, status) => {
        if (status === google.maps.DistanceMatrixStatus.OK && response) {
          const optimizedOrder = this.solveTSP(response.rows, waypoints)
          resolve(optimizedOrder)
        } else {
          console.warn('Distance matrix failed, using original order:', status)
          resolve(waypoints)
        }
      })
    })
  }

  private solveTSP(distanceMatrix: google.maps.DistanceMatrixResponseRow[], waypoints: RouteWaypoint[]): RouteWaypoint[] {
    // Simple nearest neighbor algorithm for TSP
    const n = waypoints.length
    const visited = new Array(n).fill(false)
    const result: RouteWaypoint[] = []
    
    // Start with the first waypoint (could be optimized to start with highest priority)
    let current = 0
    visited[current] = true
    result.push(waypoints[current])

    for (let i = 1; i < n; i++) {
      let nearest = -1
      let minDistance = Infinity

      for (let j = 0; j < n; j++) {
        if (!visited[j] && distanceMatrix[current].elements[j].distance) {
          const distance = distanceMatrix[current].elements[j].distance.value
          if (distance < minDistance) {
            minDistance = distance
            nearest = j
          }
        }
      }

      if (nearest !== -1) {
        visited[nearest] = true
        result.push(waypoints[nearest])
        current = nearest
      }
    }

    return result
  }

  private async calculateRouteSegments(
    waypoints: RouteWaypoint[],
    options: RouteOptimizationOptions
  ): Promise<RouteSegment[]> {
    const segments: RouteSegment[] = []

    for (let i = 0; i < waypoints.length - 1; i++) {
      const from = waypoints[i]
      const to = waypoints[i + 1]

      const segment = await this.calculateSegment(from, to, options)
      segments.push(segment)
    }

    return segments
  }

  private calculateSegment(
    from: RouteWaypoint,
    to: RouteWaypoint,
    options: RouteOptimizationOptions
  ): Promise<RouteSegment> {
    return new Promise((resolve, reject) => {
      this.directionsService!.route({
        origin: new google.maps.LatLng(from.latitude, from.longitude),
        destination: new google.maps.LatLng(to.latitude, to.longitude),
        travelMode: google.maps.TravelMode[options.travelMode],
        avoidHighways: options.avoidHighways || false,
        avoidTolls: options.avoidTolls || false
      }, (response, status) => {
        if (status === google.maps.DirectionsStatus.OK && response) {
          const route = response.routes[0]
          const leg = route.legs[0]

          const segment: RouteSegment = {
            from,
            to,
            distance: leg.distance?.value || 0,
            duration: leg.duration?.value || 0,
            polyline: route.overview_polyline,
            instructions: leg.steps?.map(step => step.instructions) || []
          }

          resolve(segment)
        } else {
          reject(new Error(`Failed to calculate route segment: ${status}`))
        }
      })
    })
  }

  private estimateTravelCosts(
    totalDistance: number,
    segmentCount: number,
    options: RouteOptimizationOptions
  ) {
    const distanceKm = totalDistance / 1000

    // Rough cost estimates (can be made more sophisticated)
    const costs = {
      fuel: 0,
      tolls: 0,
      parking: 0
    }

    if (options.travelMode === 'DRIVING') {
      // Fuel cost estimation (assuming 8L/100km and $1.50/L)
      costs.fuel = Math.round((distanceKm * 8 / 100) * 1.50 * 100) / 100

      // Toll estimation (rough estimate)
      if (!options.avoidTolls) {
        costs.tolls = Math.round(distanceKm * 0.05 * 100) / 100
      }

      // Parking estimation (per stop)
      costs.parking = segmentCount * 5 // $5 per stop
    }

    return costs
  }

  formatDuration(seconds: number): string {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)

    if (hours > 0) {
      return `${hours}h ${minutes}m`
    }
    return `${minutes}m`
  }

  formatDistance(meters: number): string {
    const km = meters / 1000
    if (km >= 1) {
      return `${Math.round(km * 10) / 10} km`
    }
    return `${Math.round(meters)} m`
  }
}

export const routeOptimizationService = new RouteOptimizationService()
