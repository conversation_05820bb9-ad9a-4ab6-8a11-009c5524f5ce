'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/Button'
import { Badge } from '@/components/ui/Badge'
import { 
  ClockIcon, 
  NavigationIcon, 
  RefreshCwIcon,
  MapPinIcon,
  CalendarIcon,
  AlertCircleIcon
} from 'lucide-react'
import { travelTimeService, type TravelMatrix, type TravelTimeRequest } from '@/lib/services/travel-time-service'
import toast from 'react-hot-toast'

interface TravelTimeMatrixProps {
  destinations: Array<{
    id: string
    name: string
    latitude: number
    longitude: number
    category: string
  }>
  className?: string
}

export default function TravelTimeMatrix({ destinations, className = '' }: TravelTimeMatrixProps) {
  const [travelMatrix, setTravelMatrix] = useState<TravelMatrix | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [travelMode, setTravelMode] = useState<'DRIVING' | 'WALKING' | 'TRANSIT' | 'BICYCLING'>('DRIVING')
  const [departureTime, setDepartureTime] = useState<Date>(new Date())
  const [showTrafficData, setShowTrafficData] = useState(true)

  useEffect(() => {
    if (destinations.length >= 2) {
      calculateTravelTimes()
    }
  }, [destinations, travelMode])

  const calculateTravelTimes = async () => {
    if (destinations.length < 2) {
      toast.error('At least 2 destinations are required')
      return
    }

    setIsLoading(true)
    try {
      const matrix = await travelTimeService.calculateTravelMatrix(
        destinations,
        destinations,
        travelMode,
        departureTime
      )
      setTravelMatrix(matrix)
    } catch (error) {
      console.error('Failed to calculate travel times:', error)
      toast.error(`Failed to calculate travel times: ${error.message}`)
    } finally {
      setIsLoading(false)
    }
  }

  const getTravelModeColor = (mode: string) => {
    switch (mode) {
      case 'DRIVING': return 'bg-blue-100 text-blue-800'
      case 'WALKING': return 'bg-green-100 text-green-800'
      case 'TRANSIT': return 'bg-purple-100 text-purple-800'
      case 'BICYCLING': return 'bg-orange-100 text-orange-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'accommodation': return '🏨'
      case 'restaurant': return '🍽️'
      case 'attraction': return '🎯'
      case 'transport': return '🚌'
      case 'shopping': return '🛍️'
      default: return '📍'
    }
  }

  const formatDateTime = (date: Date) => {
    return date.toLocaleString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  if (destinations.length < 2) {
    return (
      <div className={`bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center ${className}`}>
        <MapPinIcon className="h-12 w-12 mx-auto mb-4 text-gray-300" />
        <p className="text-gray-500">Add at least 2 destinations to see travel times</p>
      </div>
    )
  }

  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className}`}>
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <ClockIcon className="h-5 w-5 text-blue-600" />
            <h3 className="font-semibold text-gray-900">Travel Times</h3>
            <Badge variant="secondary">{destinations.length} destinations</Badge>
          </div>
          
          <Button
            onClick={calculateTravelTimes}
            disabled={isLoading}
            size="sm"
            variant="outline"
          >
            {isLoading ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2" />
            ) : (
              <RefreshCwIcon className="h-4 w-4 mr-2" />
            )}
            {isLoading ? 'Calculating...' : 'Refresh'}
          </Button>
        </div>

        {/* Controls */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Travel Mode
            </label>
            <select
              value={travelMode}
              onChange={(e) => setTravelMode(e.target.value as any)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="DRIVING">🚗 Driving</option>
              <option value="WALKING">🚶 Walking</option>
              <option value="TRANSIT">🚌 Public Transit</option>
              <option value="BICYCLING">🚴 Bicycling</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Departure Time
            </label>
            <input
              type="datetime-local"
              value={departureTime.toISOString().slice(0, 16)}
              onChange={(e) => setDepartureTime(new Date(e.target.value))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {travelMode === 'DRIVING' && (
            <div className="flex items-end">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={showTrafficData}
                  onChange={(e) => setShowTrafficData(e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="ml-2 text-sm text-gray-700">Include traffic data</span>
              </label>
            </div>
          )}
        </div>
      </div>

      {/* Travel Time Matrix */}
      {travelMatrix && (
        <div className="p-4">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr>
                  <th className="text-left p-2 text-sm font-medium text-gray-700">From / To</th>
                  {travelMatrix.destinations.map((dest, index) => (
                    <th key={index} className="text-center p-2 text-sm font-medium text-gray-700 min-w-[120px]">
                      <div className="flex flex-col items-center space-y-1">
                        <span className="text-lg">{getCategoryIcon(destinations[index].category)}</span>
                        <span className="text-xs truncate max-w-[100px]">{dest.name}</span>
                      </div>
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {travelMatrix.origins.map((origin, originIndex) => (
                  <tr key={originIndex} className="border-t border-gray-200">
                    <td className="p-2 font-medium text-gray-900">
                      <div className="flex items-center space-x-2">
                        <span className="text-lg">{getCategoryIcon(destinations[originIndex].category)}</span>
                        <span className="text-sm truncate max-w-[120px]">{origin.name}</span>
                      </div>
                    </td>
                    {travelMatrix.results[originIndex].map((result, destIndex) => (
                      <td key={destIndex} className="p-2 text-center">
                        {originIndex === destIndex ? (
                          <div className="text-gray-400 text-sm">—</div>
                        ) : result.status === 'OK' ? (
                          <div className="space-y-1">
                            <div className="text-sm font-medium text-gray-900">
                              {result.duration.text}
                            </div>
                            <div className="text-xs text-gray-600">
                              {result.distance.text}
                            </div>
                            {result.durationInTraffic && showTrafficData && (
                              <div className="text-xs text-orange-600">
                                {result.durationInTraffic.text} (traffic)
                              </div>
                            )}
                          </div>
                        ) : (
                          <div className="text-red-500 text-xs">
                            <AlertCircleIcon className="h-4 w-4 mx-auto mb-1" />
                            N/A
                          </div>
                        )}
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Summary */}
          <div className="mt-4 p-3 bg-gray-50 rounded-lg">
            <div className="flex items-center justify-between text-sm">
              <div className="flex items-center space-x-4">
                <Badge className={getTravelModeColor(travelMode)}>
                  {travelTimeService.getTravelModeIcon(travelMode)} {travelMode}
                </Badge>
                <span className="text-gray-600">
                  <CalendarIcon className="h-4 w-4 inline mr-1" />
                  {formatDateTime(departureTime)}
                </span>
              </div>
              <div className="text-gray-600">
                Matrix: {destinations.length}×{destinations.length} destinations
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Loading State */}
      {isLoading && (
        <div className="p-8 text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Calculating travel times...</p>
        </div>
      )}
    </div>
  )
}
