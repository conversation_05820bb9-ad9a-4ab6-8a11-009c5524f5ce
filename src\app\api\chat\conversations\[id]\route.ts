import { createClient } from '@/lib/supabase/server'
import { NextRequest, NextResponse } from 'next/server'
import { withSecurity } from '@/lib/security/middleware'
import { addSecurityHeaders } from '@/lib/security/utils'
import { rateLimitConfigs } from '@/lib/security/rate-limit'
import { handleSecureError } from '@/lib/security/error-handler'

// DELETE /api/chat/conversations/[id] - Delete conversation
export const DELETE = withSecurity(
  async (request: NextRequest, context: any) => {
    try {
      const params = await context.params
      const conversationId = params.id

      if (!conversationId || typeof conversationId !== 'string') {
        return NextResponse.json({ 
          error: { code: 'VALIDATION_ERROR', message: 'Invalid conversation ID' } 
        }, { status: 400 })
      }

      const supabase = await createClient()
      const user = context.user

      // First verify the conversation belongs to the user
      const { data: conversation, error: fetchError } = await supabase
        .from('chat_conversations')
        .select('id')
        .eq('id', conversationId)
        .eq('user_id', user.id)
        .single()

      if (fetchError || !conversation) {
        return NextResponse.json({ 
          error: { code: 'NOT_FOUND', message: 'Conversation not found' } 
        }, { status: 404 })
      }

      // Delete the conversation (messages will be deleted by cascade)
      const { error: deleteError } = await supabase
        .from('chat_conversations')
        .delete()
        .eq('id', conversationId)
        .eq('user_id', user.id)

      if (deleteError) throw deleteError

      const response = NextResponse.json({ success: true })
      return addSecurityHeaders(response)
    } catch (error) {
      return handleSecureError(error)
    }
  },
  {
    requireAuth: true,
    rateLimit: rateLimitConfigs.api,
    auditLog: true
  }
)

// PATCH /api/chat/conversations/[id] - Update conversation title
export const PATCH = withSecurity(
  async (request: NextRequest, context: any) => {
    try {
      const params = await context.params
      const conversationId = params.id
      const requestData = await request.json()
      const { title } = requestData

      if (!conversationId || typeof conversationId !== 'string') {
        return NextResponse.json({ 
          error: { code: 'VALIDATION_ERROR', message: 'Invalid conversation ID' } 
        }, { status: 400 })
      }

      if (!title || typeof title !== 'string' || title.length > 100) {
        return NextResponse.json({ 
          error: { code: 'VALIDATION_ERROR', message: 'Invalid title' } 
        }, { status: 400 })
      }

      const supabase = await createClient()
      const user = context.user

      // Update the conversation title
      const { data: conversation, error } = await supabase
        .from('chat_conversations')
        .update({ title: title.trim() })
        .eq('id', conversationId)
        .eq('user_id', user.id)
        .select('id, title, created_at, updated_at')
        .single()

      if (error) throw error

      if (!conversation) {
        return NextResponse.json({ 
          error: { code: 'NOT_FOUND', message: 'Conversation not found' } 
        }, { status: 404 })
      }

      const response = NextResponse.json({ conversation })
      return addSecurityHeaders(response)
    } catch (error) {
      return handleSecureError(error)
    }
  },
  {
    requireAuth: true,
    rateLimit: rateLimitConfigs.api,
    auditLog: true
  }
)
