import { createClient } from '@/lib/supabase/server'
import { NextRequest, NextResponse } from 'next/server'
import { withSecurity } from '@/lib/security/middleware'
import { addSecurityHeaders } from '@/lib/security/utils'
import { rateLimitConfigs } from '@/lib/security/rate-limit'
import { handleSecureError } from '@/lib/security/error-handler'

// GET /api/trips/[id]/destinations - Get destinations for a trip
export const GET = withSecurity(
  async (request: NextRequest, context: any) => {
    try {
      const params = await context.params
      const { id } = params
      const supabase = await createClient()

      const { data: destinations, error } = await supabase
        .from('trip_destinations')
        .select(`
          *,
          trip_activities (*)
        `)
        .eq('trip_id', id)
        .order('order_index')

      if (error) throw error

      const response = NextResponse.json({ destinations: destinations || [] })
      return addSecurityHeaders(response)
    } catch (error) {
      return handleSecureError(error)
    }
  },
  {
    requireAuth: true,
    rateLimit: rateLimitConfigs.api,
    auditLog: true
  }
)

// POST /api/trips/[id]/destinations - Add destination to trip
export const POST = withSecurity(
  async (request: NextRequest, context: any) => {
    try {
      const params = await context.params
      const { id } = params
      const requestData = await request.json()
      const supabase = await createClient()

      const { data: destination, error } = await supabase
        .from('trip_destinations')
        .insert([
          {
            trip_id: id,
            ...requestData
          }
        ])
        .select()
        .single()

      if (error) throw error

      const response = NextResponse.json({ destination })
      return addSecurityHeaders(response)
    } catch (error) {
      return handleSecureError(error)
    }
  },
  {
    requireAuth: true,
    rateLimit: rateLimitConfigs.api,
    auditLog: true
  }
)
