export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          email: string
          full_name: string | null
          avatar_url: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          full_name?: string | null
          avatar_url?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          full_name?: string | null
          avatar_url?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      tasks: {
        Row: {
          id: string
          user_id: string
          title: string
          description: string | null
          completed: boolean
          priority: 'low' | 'medium' | 'high'
          due_date: string | null
          category: string | null
          tags: string[]
          estimated_duration: number | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          title: string
          description?: string | null
          completed?: boolean
          priority?: 'low' | 'medium' | 'high'
          due_date?: string | null
          category?: string | null
          tags?: string[]
          estimated_duration?: number | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          title?: string
          description?: string | null
          completed?: boolean
          priority?: 'low' | 'medium' | 'high'
          due_date?: string | null
          category?: string | null
          tags?: string[]
          estimated_duration?: number | null
          created_at?: string
          updated_at?: string
        }
      }
      budget_categories: {
        Row: {
          id: string
          user_id: string
          name: string
          type: 'income' | 'expense'
          color: string
          budget_limit: number | null
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          name: string
          type: 'income' | 'expense'
          color?: string
          budget_limit?: number | null
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          name?: string
          type?: 'income' | 'expense'
          color?: string
          budget_limit?: number | null
          created_at?: string
        }
      }
      transactions: {
        Row: {
          id: string
          user_id: string
          category_id: string | null
          amount: number
          description: string
          date: string
          type: 'income' | 'expense'
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          category_id?: string | null
          amount: number
          description: string
          date: string
          type: 'income' | 'expense'
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          category_id?: string | null
          amount?: number
          description?: string
          date?: string
          type?: 'income' | 'expense'
          created_at?: string
          updated_at?: string
        }
      }
      shopping_lists: {
        Row: {
          id: string
          user_id: string
          name: string
          description: string | null
          store_name: string | null
          store_address: string | null
          is_shared: boolean
          shared_with: string[]
          total_estimated_cost: number
          total_actual_cost: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          name: string
          description?: string | null
          store_name?: string | null
          store_address?: string | null
          is_shared?: boolean
          shared_with?: string[]
          total_estimated_cost?: number
          total_actual_cost?: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          name?: string
          description?: string | null
          store_name?: string | null
          store_address?: string | null
          is_shared?: boolean
          shared_with?: string[]
          total_estimated_cost?: number
          total_actual_cost?: number
          created_at?: string
          updated_at?: string
        }
      }
      shopping_list_items: {
        Row: {
          id: string
          shopping_list_id: string
          name: string
          quantity: number
          unit: string | null
          completed: boolean
          notes: string | null
          estimated_price: number | null
          actual_price: number | null
          category: string | null
          priority: number
          recipe_id: string | null
          store_section: string | null
          brand_preference: string | null
          is_organic: boolean
          barcode: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          shopping_list_id: string
          name: string
          quantity?: number
          unit?: string | null
          completed?: boolean
          notes?: string | null
          estimated_price?: number | null
          actual_price?: number | null
          category?: string | null
          priority?: number
          recipe_id?: string | null
          store_section?: string | null
          brand_preference?: string | null
          is_organic?: boolean
          barcode?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          shopping_list_id?: string
          name?: string
          quantity?: number
          unit?: string | null
          completed?: boolean
          notes?: string | null
          estimated_price?: number | null
          actual_price?: number | null
          category?: string | null
          priority?: number
          recipe_id?: string | null
          store_section?: string | null
          brand_preference?: string | null
          is_organic?: boolean
          barcode?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      recipes: {
        Row: {
          id: string
          user_id: string
          title: string
          description: string | null
          ingredients: any[]
          instructions: any[]
          prep_time: number | null
          cook_time: number | null
          servings: number | null
          difficulty: 'easy' | 'medium' | 'hard'
          cuisine: string | null
          tags: string[]
          image_url: string | null
          source_url: string | null
          nutritional_info: any | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          title: string
          description?: string | null
          ingredients?: any[]
          instructions?: any[]
          prep_time?: number | null
          cook_time?: number | null
          servings?: number | null
          difficulty?: 'easy' | 'medium' | 'hard'
          cuisine?: string | null
          tags?: string[]
          image_url?: string | null
          source_url?: string | null
          nutritional_info?: any | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          title?: string
          description?: string | null
          ingredients?: any[]
          instructions?: any[]
          prep_time?: number | null
          cook_time?: number | null
          servings?: number | null
          difficulty?: 'easy' | 'medium' | 'hard'
          cuisine?: string | null
          tags?: string[]
          image_url?: string | null
          source_url?: string | null
          nutritional_info?: any | null
          created_at?: string
          updated_at?: string
        }
      }
      meal_plans: {
        Row: {
          id: string
          user_id: string
          recipe_id: string
          planned_date: string
          meal_type: 'breakfast' | 'lunch' | 'dinner' | 'snack'
          servings: number
          notes: string | null
          completed: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          recipe_id: string
          planned_date: string
          meal_type: 'breakfast' | 'lunch' | 'dinner' | 'snack'
          servings?: number
          notes?: string | null
          completed?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          recipe_id?: string
          planned_date?: string
          meal_type?: 'breakfast' | 'lunch' | 'dinner' | 'snack'
          servings?: number
          notes?: string | null
          completed?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      recipe_ratings: {
        Row: {
          id: string
          user_id: string
          recipe_id: string
          rating: number
          review: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          recipe_id: string
          rating: number
          review?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          recipe_id?: string
          rating?: number
          review?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      chat_conversations: {
        Row: {
          id: string
          user_id: string
          title: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          title: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          title?: string
          created_at?: string
          updated_at?: string
        }
      }
      chat_messages: {
        Row: {
          id: string
          conversation_id: string
          role: 'user' | 'assistant'
          content: string
          created_at: string
        }
        Insert: {
          id?: string
          conversation_id: string
          role: 'user' | 'assistant'
          content: string
          created_at?: string
        }
        Update: {
          id?: string
          conversation_id?: string
          role?: 'user' | 'assistant'
          content?: string
          created_at?: string
        }
      }
      stores: {
        Row: {
          id: string
          name: string
          address: string | null
          phone: string | null
          website: string | null
          store_type: string | null
          latitude: number | null
          longitude: number | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          address?: string | null
          phone?: string | null
          website?: string | null
          store_type?: string | null
          latitude?: number | null
          longitude?: number | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          address?: string | null
          phone?: string | null
          website?: string | null
          store_type?: string | null
          latitude?: number | null
          longitude?: number | null
          created_at?: string
          updated_at?: string
        }
      }
      store_sections: {
        Row: {
          id: string
          store_id: string
          name: string
          section_order: number
          description: string | null
          created_at: string
        }
        Insert: {
          id?: string
          store_id: string
          name: string
          section_order?: number
          description?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          store_id?: string
          name?: string
          section_order?: number
          description?: string | null
          created_at?: string
        }
      }
      price_history: {
        Row: {
          id: string
          item_name: string
          store_id: string | null
          price: number
          unit: string | null
          brand: string | null
          recorded_at: string
          user_id: string
        }
        Insert: {
          id?: string
          item_name: string
          store_id?: string | null
          price: number
          unit?: string | null
          brand?: string | null
          recorded_at?: string
          user_id: string
        }
        Update: {
          id?: string
          item_name?: string
          store_id?: string | null
          price?: number
          unit?: string | null
          brand?: string | null
          recorded_at?: string
          user_id?: string
        }
      }
      shopping_list_collaborators: {
        Row: {
          id: string
          shopping_list_id: string
          user_id: string
          permission_level: 'view' | 'edit' | 'admin'
          invited_by: string
          invited_at: string
          accepted_at: string | null
        }
        Insert: {
          id?: string
          shopping_list_id: string
          user_id: string
          permission_level?: 'view' | 'edit' | 'admin'
          invited_by: string
          invited_at?: string
          accepted_at?: string | null
        }
        Update: {
          id?: string
          shopping_list_id?: string
          user_id?: string
          permission_level?: 'view' | 'edit' | 'admin'
          invited_by?: string
          invited_at?: string
          accepted_at?: string | null
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      get_user_stats: {
        Args: {
          user_uuid: string
        }
        Returns: any
      }
    }
    Enums: {
      [_ in never]: never
    }
  }
}
