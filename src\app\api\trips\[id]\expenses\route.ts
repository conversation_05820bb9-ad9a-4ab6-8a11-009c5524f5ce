import { createClient } from '@/lib/supabase/server'
import { NextRequest, NextResponse } from 'next/server'
import { withSecurity } from '@/lib/security/middleware'
import { addSecurityHeaders } from '@/lib/security/utils'
import { rateLimitConfigs } from '@/lib/security/rate-limit'
import { handleSecureError } from '@/lib/security/error-handler'

// GET /api/trips/[id]/expenses - Get expenses for a trip
export const GET = withSecurity(
  async (request: NextRequest, context: any) => {
    try {
      const params = await context.params
      const { id } = params
      const supabase = await createClient()

      const { data: expenses, error } = await supabase
        .from('trip_expenses')
        .select(`
          *,
          trip_destinations (name),
          trip_activities (title)
        `)
        .eq('trip_id', id)
        .order('expense_date', { ascending: false })

      if (error) throw error

      const response = NextResponse.json({ expenses: expenses || [] })
      return addSecurityHeaders(response)
    } catch (error) {
      return handleSecureError(error)
    }
  },
  {
    requireAuth: true,
    rateLimit: rateLimitConfigs.api,
    auditLog: true
  }
)

// POST /api/trips/[id]/expenses - Add expense to trip
export const POST = withSecurity(
  async (request: NextRequest, context: any) => {
    try {
      const params = await context.params
      const { id } = params
      const requestData = await request.json()
      const supabase = await createClient()

      // Calculate USD amount if different currency
      let amountUsd = requestData.amount
      if (requestData.currency !== 'USD' && requestData.exchange_rate) {
        amountUsd = requestData.amount * requestData.exchange_rate
      }

      const { data: expense, error } = await supabase
        .from('trip_expenses')
        .insert([
          {
            trip_id: id,
            ...requestData,
            amount_usd: amountUsd
          }
        ])
        .select()
        .single()

      if (error) throw error

      // Update trip budget_spent
      const { error: updateError } = await supabase
        .rpc('update_trip_budget_spent', { trip_id: id })

      if (updateError) console.error('Error updating trip budget:', updateError)

      const response = NextResponse.json({ expense })
      return addSecurityHeaders(response)
    } catch (error) {
      return handleSecureError(error)
    }
  },
  {
    requireAuth: true,
    rateLimit: rateLimitConfigs.api,
    auditLog: true
  }
)
