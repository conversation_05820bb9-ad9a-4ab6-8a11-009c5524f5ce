import { createClient } from '@/lib/supabase/server'
import { NextRequest, NextResponse } from 'next/server'
import { withSecurity } from '@/lib/security/middleware'
import { addSecurityHeaders } from '@/lib/security/utils'
import { rateLimitConfigs } from '@/lib/security/rate-limit'
import { handleSecureError } from '@/lib/security/error-handler'

// GET /api/trips/[id]/activities - Get activities for a trip
export const GET = withSecurity(
  async (request: NextRequest, context: any) => {
    try {
      const params = await context.params
      const { id } = params
      const supabase = await createClient()

      const { data: activities, error } = await supabase
        .from('trip_activities')
        .select(`
          *,
          trip_destinations (name, city, country)
        `)
        .eq('trip_id', id)
        .order('scheduled_date', { ascending: true })

      if (error) throw error

      const response = NextResponse.json({ activities: activities || [] })
      return addSecurityHeaders(response)
    } catch (error) {
      return handleSecureError(error)
    }
  },
  {
    requireAuth: true,
    rateLimit: rateLimitConfigs.api,
    auditLog: true
  }
)

// POST /api/trips/[id]/activities - Add activity to trip
export const POST = withSecurity(
  async (request: NextRequest, context: any) => {
    try {
      const params = await context.params
      const { id } = params
      const requestData = await request.json()
      const supabase = await createClient()

      const { data: activity, error } = await supabase
        .from('trip_activities')
        .insert([
          {
            trip_id: id,
            ...requestData
          }
        ])
        .select()
        .single()

      if (error) throw error

      const response = NextResponse.json({ activity })
      return addSecurityHeaders(response)
    } catch (error) {
      return handleSecureError(error)
    }
  },
  {
    requireAuth: true,
    rateLimit: rateLimitConfigs.api,
    auditLog: true
  }
)
