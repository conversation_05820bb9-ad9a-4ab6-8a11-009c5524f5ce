-- Add Trip Planner Tables
-- Only create tables that don't exist yet

-- Create trips table
CREATE TABLE IF NOT EXISTS public.trips (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
  title TEXT NOT NULL,
  description TEXT,
  destination_country TEXT NOT NULL,
  destination_city TEXT,
  start_date DATE NOT NULL,
  end_date DATE NOT NULL,
  trip_type TEXT CHECK (trip_type IN ('leisure', 'business', 'adventure', 'family', 'romantic', 'solo', 'group')) DEFAULT 'leisure',
  status TEXT CHECK (status IN ('planning', 'booked', 'in_progress', 'completed', 'cancelled')) DEFAULT 'planning',
  budget_total DECIMAL(10,2),
  budget_spent DECIMAL(10,2) DEFAULT 0,
  currency TEXT DEFAULT 'USD',
  traveler_count INTEGER DEFAULT 1,
  is_shared BOOLEAN DEFAULT FALSE,
  cover_image_url TEXT,
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create trip_destinations table
CREATE TABLE IF NOT EXISTS public.trip_destinations (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  trip_id UUID REFERENCES public.trips(id) ON DELETE CASCADE NOT NULL,
  name TEXT NOT NULL,
  country TEXT NOT NULL,
  city TEXT,
  latitude DECIMAL(10, 8),
  longitude DECIMAL(11, 8),
  arrival_date DATE,
  departure_date DATE,
  order_index INTEGER NOT NULL DEFAULT 0,
  accommodation_name TEXT,
  accommodation_address TEXT,
  accommodation_cost DECIMAL(10,2),
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create trip_activities table
CREATE TABLE IF NOT EXISTS public.trip_activities (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  trip_id UUID REFERENCES public.trips(id) ON DELETE CASCADE NOT NULL,
  destination_id UUID REFERENCES public.trip_destinations(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  description TEXT,
  category TEXT CHECK (category IN ('sightseeing', 'dining', 'entertainment', 'shopping', 'transport', 'accommodation', 'adventure', 'cultural', 'relaxation', 'business')) NOT NULL,
  scheduled_date DATE,
  scheduled_time TIME,
  duration_minutes INTEGER,
  cost DECIMAL(10,2),
  currency TEXT DEFAULT 'USD',
  location_name TEXT,
  location_address TEXT,
  latitude DECIMAL(10, 8),
  longitude DECIMAL(11, 8),
  booking_reference TEXT,
  booking_url TEXT,
  status TEXT CHECK (status IN ('planned', 'booked', 'completed', 'cancelled')) DEFAULT 'planned',
  priority TEXT CHECK (priority IN ('low', 'medium', 'high', 'must_do')) DEFAULT 'medium',
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create trip_expenses table
CREATE TABLE IF NOT EXISTS public.trip_expenses (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  trip_id UUID REFERENCES public.trips(id) ON DELETE CASCADE NOT NULL,
  destination_id UUID REFERENCES public.trip_destinations(id) ON DELETE CASCADE,
  activity_id UUID REFERENCES public.trip_activities(id) ON DELETE CASCADE,
  category TEXT CHECK (category IN ('accommodation', 'transport', 'food', 'activities', 'shopping', 'fuel', 'parking', 'tolls', 'insurance', 'visas', 'other')) NOT NULL,
  subcategory TEXT,
  description TEXT NOT NULL,
  amount DECIMAL(10,2) NOT NULL,
  currency TEXT DEFAULT 'USD',
  exchange_rate DECIMAL(10, 6) DEFAULT 1.0,
  amount_usd DECIMAL(10,2) NOT NULL,
  expense_date DATE NOT NULL,
  payment_method TEXT,
  receipt_url TEXT,
  is_shared_expense BOOLEAN DEFAULT FALSE,
  shared_with_count INTEGER DEFAULT 1,
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create trip_transport table
CREATE TABLE IF NOT EXISTS public.trip_transport (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  trip_id UUID REFERENCES public.trips(id) ON DELETE CASCADE NOT NULL,
  from_destination_id UUID REFERENCES public.trip_destinations(id) ON DELETE CASCADE,
  to_destination_id UUID REFERENCES public.trip_destinations(id) ON DELETE CASCADE,
  transport_type TEXT CHECK (transport_type IN ('flight', 'train', 'bus', 'car_rental', 'taxi', 'rideshare', 'ferry', 'walking', 'cycling', 'other')) NOT NULL,
  provider_name TEXT,
  departure_datetime TIMESTAMP WITH TIME ZONE,
  arrival_datetime TIMESTAMP WITH TIME ZONE,
  duration_minutes INTEGER,
  distance_km DECIMAL(8, 2),
  cost DECIMAL(10,2),
  currency TEXT DEFAULT 'USD',
  booking_reference TEXT,
  booking_url TEXT,
  from_location TEXT,
  to_location TEXT,
  from_latitude DECIMAL(10, 8),
  from_longitude DECIMAL(11, 8),
  to_latitude DECIMAL(10, 8),
  to_longitude DECIMAL(11, 8),
  fuel_cost DECIMAL(10,2),
  fuel_consumption_liters DECIMAL(6, 2),
  toll_costs DECIMAL(10,2),
  parking_costs DECIMAL(10,2),
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create trip_collaborators table
CREATE TABLE IF NOT EXISTS public.trip_collaborators (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  trip_id UUID REFERENCES public.trips(id) ON DELETE CASCADE NOT NULL,
  user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
  permission_level TEXT CHECK (permission_level IN ('view', 'edit', 'admin')) DEFAULT 'view',
  invited_by UUID REFERENCES public.profiles(id) ON DELETE SET NULL,
  invited_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  accepted_at TIMESTAMP WITH TIME ZONE,
  status TEXT CHECK (status IN ('pending', 'accepted', 'declined')) DEFAULT 'pending',
  UNIQUE(trip_id, user_id)
);

-- Create trip_documents table
CREATE TABLE IF NOT EXISTS public.trip_documents (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  trip_id UUID REFERENCES public.trips(id) ON DELETE CASCADE NOT NULL,
  title TEXT NOT NULL,
  document_type TEXT CHECK (document_type IN ('passport', 'visa', 'ticket', 'reservation', 'insurance', 'itinerary', 'map', 'other')) NOT NULL,
  file_url TEXT,
  file_name TEXT,
  file_size INTEGER,
  expiry_date DATE,
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_trips_user_id ON public.trips(user_id);
CREATE INDEX IF NOT EXISTS idx_trips_dates ON public.trips(start_date, end_date);
CREATE INDEX IF NOT EXISTS idx_trip_destinations_trip_id ON public.trip_destinations(trip_id);
CREATE INDEX IF NOT EXISTS idx_trip_activities_trip_id ON public.trip_activities(trip_id);
CREATE INDEX IF NOT EXISTS idx_trip_activities_destination_id ON public.trip_activities(destination_id);
CREATE INDEX IF NOT EXISTS idx_trip_activities_date ON public.trip_activities(scheduled_date);
CREATE INDEX IF NOT EXISTS idx_trip_expenses_trip_id ON public.trip_expenses(trip_id);
CREATE INDEX IF NOT EXISTS idx_trip_expenses_date ON public.trip_expenses(expense_date);
CREATE INDEX IF NOT EXISTS idx_trip_transport_trip_id ON public.trip_transport(trip_id);
CREATE INDEX IF NOT EXISTS idx_trip_collaborators_trip_id ON public.trip_collaborators(trip_id);
CREATE INDEX IF NOT EXISTS idx_trip_collaborators_user_id ON public.trip_collaborators(user_id);

-- Enable Row Level Security
ALTER TABLE public.trips ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.trip_destinations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.trip_activities ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.trip_expenses ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.trip_transport ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.trip_collaborators ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.trip_documents ENABLE ROW LEVEL SECURITY;
