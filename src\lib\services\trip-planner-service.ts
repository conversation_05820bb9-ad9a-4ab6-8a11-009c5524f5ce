export interface Trip {
  id: string
  user_id: string
  title: string
  description?: string
  destination_country: string
  destination_city?: string
  start_date: string
  end_date: string
  trip_type: 'leisure' | 'business' | 'adventure' | 'family' | 'romantic' | 'solo' | 'group'
  status: 'planning' | 'booked' | 'in_progress' | 'completed' | 'cancelled'
  budget_total?: number
  budget_spent: number
  currency: string
  traveler_count: number
  is_shared: boolean
  cover_image_url?: string
  notes?: string
  created_at: string
  updated_at: string
  trip_destinations?: TripDestination[]
}

export interface TripDestination {
  id: string
  trip_id: string
  name: string
  country: string
  city?: string
  latitude?: number
  longitude?: number
  arrival_date?: string
  departure_date?: string
  order_index: number
  accommodation_name?: string
  accommodation_address?: string
  accommodation_cost?: number
  notes?: string
  created_at: string
  updated_at: string
  trip_activities?: TripActivity[]
}

export interface TripActivity {
  id: string
  trip_id: string
  destination_id?: string
  title: string
  description?: string
  category: 'sightseeing' | 'dining' | 'entertainment' | 'shopping' | 'transport' | 'accommodation' | 'adventure' | 'cultural' | 'relaxation' | 'business'
  scheduled_date?: string
  scheduled_time?: string
  duration_minutes?: number
  cost?: number
  currency: string
  location_name?: string
  location_address?: string
  latitude?: number
  longitude?: number
  booking_reference?: string
  booking_url?: string
  status: 'planned' | 'booked' | 'completed' | 'cancelled'
  priority: 'low' | 'medium' | 'high' | 'must_do'
  notes?: string
  created_at: string
  updated_at: string
}

export interface TripExpense {
  id: string
  trip_id: string
  destination_id?: string
  activity_id?: string
  category: 'accommodation' | 'transport' | 'food' | 'activities' | 'shopping' | 'fuel' | 'parking' | 'tolls' | 'insurance' | 'visas' | 'other'
  subcategory?: string
  description: string
  amount: number
  currency: string
  exchange_rate: number
  amount_usd: number
  expense_date: string
  payment_method?: string
  receipt_url?: string
  is_shared_expense: boolean
  shared_with_count: number
  notes?: string
  created_at: string
  updated_at: string
}

export interface TripTransport {
  id: string
  trip_id: string
  from_destination_id?: string
  to_destination_id?: string
  transport_type: 'flight' | 'train' | 'bus' | 'car_rental' | 'taxi' | 'rideshare' | 'ferry' | 'walking' | 'cycling' | 'other'
  provider_name?: string
  departure_datetime?: string
  arrival_datetime?: string
  duration_minutes?: number
  distance_km?: number
  cost?: number
  currency: string
  booking_reference?: string
  booking_url?: string
  from_location?: string
  to_location?: string
  from_latitude?: number
  from_longitude?: number
  to_latitude?: number
  to_longitude?: number
  fuel_cost?: number
  fuel_consumption_liters?: number
  toll_costs?: number
  parking_costs?: number
  notes?: string
  created_at: string
  updated_at: string
}

export interface CreateTripData {
  title: string
  description?: string
  destination_country: string
  destination_city?: string
  start_date: string
  end_date: string
  trip_type?: 'leisure' | 'business' | 'adventure' | 'family' | 'romantic' | 'solo' | 'group'
  budget_total?: number
  currency?: string
  traveler_count?: number
  cover_image_url?: string
  notes?: string
}

export interface UpdateTripData extends Partial<CreateTripData> {}

export interface CreateDestinationData {
  name: string
  country: string
  city?: string
  latitude?: number
  longitude?: number
  arrival_date?: string
  departure_date?: string
  order_index?: number
  accommodation_name?: string
  accommodation_address?: string
  accommodation_cost?: number
  notes?: string
}

export interface CreateActivityData {
  destination_id?: string
  title: string
  description?: string
  category: 'sightseeing' | 'dining' | 'entertainment' | 'shopping' | 'transport' | 'accommodation' | 'adventure' | 'cultural' | 'relaxation' | 'business'
  scheduled_date?: string
  scheduled_time?: string
  duration_minutes?: number
  cost?: number
  currency?: string
  location_name?: string
  location_address?: string
  latitude?: number
  longitude?: number
  booking_reference?: string
  booking_url?: string
  priority?: 'low' | 'medium' | 'high' | 'must_do'
  notes?: string
}

export interface CreateExpenseData {
  destination_id?: string
  activity_id?: string
  category: 'accommodation' | 'transport' | 'food' | 'activities' | 'shopping' | 'fuel' | 'parking' | 'tolls' | 'insurance' | 'visas' | 'other'
  subcategory?: string
  description: string
  amount: number
  currency?: string
  exchange_rate?: number
  expense_date: string
  payment_method?: string
  receipt_url?: string
  is_shared_expense?: boolean
  shared_with_count?: number
  notes?: string
}

class TripPlannerService {
  private baseUrl = '/api/trips'

  // Trip CRUD operations
  async getTrips(): Promise<Trip[]> {
    const response = await fetch(this.baseUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      throw new Error(`Failed to fetch trips: ${response.statusText}`)
    }

    const data = await response.json()
    return data.trips || []
  }

  async getTrip(id: string): Promise<Trip> {
    const response = await fetch(`${this.baseUrl}/${id}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      throw new Error('Failed to fetch trip')
    }

    const data = await response.json()
    return data.trip
  }

  async getTrip(id: string): Promise<Trip> {
    const response = await fetch(`${this.baseUrl}/${id}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      throw new Error(`Failed to fetch trip: ${response.statusText}`)
    }

    const data = await response.json()
    return data.trip
  }

  async createTrip(data: CreateTripData): Promise<Trip> {
    const response = await fetch(this.baseUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    })

    if (!response.ok) {
      throw new Error(`Failed to create trip: ${response.statusText}`)
    }

    const result = await response.json()
    return result.trip
  }

  async updateTrip(id: string, data: UpdateTripData): Promise<Trip> {
    const response = await fetch(`${this.baseUrl}/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    })

    if (!response.ok) {
      throw new Error(`Failed to update trip: ${response.statusText}`)
    }

    const result = await response.json()
    return result.trip
  }

  async deleteTrip(id: string): Promise<void> {
    const response = await fetch(`${this.baseUrl}/${id}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      throw new Error(`Failed to delete trip: ${response.statusText}`)
    }
  }

  // Destination operations
  async getDestinations(tripId: string): Promise<TripDestination[]> {
    const response = await fetch(`${this.baseUrl}/${tripId}/destinations`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      throw new Error(`Failed to fetch destinations: ${response.statusText}`)
    }

    const data = await response.json()
    return data.destinations || []
  }

  async addDestination(tripId: string, data: CreateDestinationData): Promise<TripDestination> {
    const response = await fetch(`${this.baseUrl}/${tripId}/destinations`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    })

    if (!response.ok) {
      throw new Error(`Failed to add destination: ${response.statusText}`)
    }

    const result = await response.json()
    return result.destination
  }

  // Activity operations
  async getActivities(tripId: string): Promise<TripActivity[]> {
    const response = await fetch(`${this.baseUrl}/${tripId}/activities`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      throw new Error(`Failed to fetch activities: ${response.statusText}`)
    }

    const data = await response.json()
    return data.activities || []
  }

  async addActivity(tripId: string, data: CreateActivityData): Promise<TripActivity> {
    const response = await fetch(`${this.baseUrl}/${tripId}/activities`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    })

    if (!response.ok) {
      throw new Error(`Failed to add activity: ${response.statusText}`)
    }

    const result = await response.json()
    return result.activity
  }

  // Expense operations
  async getExpenses(tripId: string): Promise<TripExpense[]> {
    const response = await fetch(`${this.baseUrl}/${tripId}/expenses`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      throw new Error(`Failed to fetch expenses: ${response.statusText}`)
    }

    const data = await response.json()
    return data.expenses || []
  }

  async addExpense(tripId: string, data: CreateExpenseData): Promise<TripExpense> {
    const response = await fetch(`${this.baseUrl}/${tripId}/expenses`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    })

    if (!response.ok) {
      throw new Error(`Failed to add expense: ${response.statusText}`)
    }

    const result = await response.json()
    return result.expense
  }

  // Utility methods
  calculateTripDuration(startDate: string, endDate: string): number {
    const start = new Date(startDate)
    const end = new Date(endDate)
    const diffTime = Math.abs(end.getTime() - start.getTime())
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  }

  calculateBudgetProgress(budgetTotal?: number, budgetSpent?: number): number {
    if (!budgetTotal || budgetTotal === 0) return 0
    return Math.min((budgetSpent || 0) / budgetTotal * 100, 100)
  }

  formatCurrency(amount: number, currency: string = 'USD'): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount)
  }
}

export const tripPlannerService = new TripPlannerService()
