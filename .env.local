# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://ocyjxnddxuhhlnguybre.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9jeWp4bmRkeHVoaGxuZ3V5YnJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE5NDY2ODYsImV4cCI6MjA2NzUyMjY4Nn0.LQJjfsT5lKAnfD7dvWMtKMjgLiFo-vjcone8yi-Gm40
# WARNING: Service role key should NEVER be exposed to client-side code
# This key bypasses RLS and should only be used in secure server environments
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9jeWp4bmRkeHVoaGxuZ3V5YnJlIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTk0NjY4NiwiZXhwIjoyMDY3NTIyNjg2fQ.MTB_bMp4Y6svK9PDpf8MI7JRki0E57gHCuVqQVDTZqc

# OpenAI Configuration (for AI chat)
OPENAI_API_KEY=********************************************************************************************************************************************************************

# Recipe Import API (optional)
SPOONACULAR_API_KEY=********************************

# Google Maps API (for trip planner maps)
# Get your API key from: https://console.cloud.google.com/google/maps-apis/
# Enable Maps JavaScript API and Places API
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=