'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/Button'
import { Badge } from '@/components/ui/Badge'
import {
  RouteIcon,
  ClockIcon,
  MapPinIcon,
  DollarSignIcon,
  NavigationIcon,
  SettingsIcon,
  ZapIcon
} from 'lucide-react'
import { routeOptimizationService, type RouteWaypoint, type OptimizedRoute, type RouteOptimizationOptions } from '@/lib/services/route-optimization-service'
import toast from 'react-hot-toast'

interface RouteOptimizerProps {
  waypoints: RouteWaypoint[]
  onRouteOptimized: (route: OptimizedRoute) => void
  className?: string
}

export default function RouteOptimizer({ waypoints, onRouteOptimized, className = '' }: RouteOptimizerProps) {
  const [isOptimizing, setIsOptimizing] = useState(false)
  const [optimizedRoute, setOptimizedRoute] = useState<OptimizedRoute | null>(null)
  const [showSettings, setShowSettings] = useState(false)
  const [options, setOptions] = useState<RouteOptimizationOptions>({
    travelMode: 'DRIVING',
    optimizeOrder: true,
    avoidTolls: false,
    avoidHighways: false
  })

  const handleOptimizeRoute = async () => {
    if (waypoints.length < 2) {
      toast.error('At least 2 destinations are required for route optimization')
      return
    }

    setIsOptimizing(true)
    try {
      const route = await routeOptimizationService.optimizeRoute(waypoints, options)
      setOptimizedRoute(route)
      onRouteOptimized(route)
      toast.success('Route optimized successfully!')
    } catch (error) {
      console.error('Route optimization failed:', error)
      toast.error(`Failed to optimize route: ${error.message}`)
    } finally {
      setIsOptimizing(false)
    }
  }

  const getTravelModeIcon = (mode: string) => {
    switch (mode) {
      case 'DRIVING': return '🚗'
      case 'WALKING': return '🚶'
      case 'TRANSIT': return '🚌'
      case 'BICYCLING': return '🚴'
      default: return '🚗'
    }
  }

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'accommodation': return '🏨'
      case 'restaurant': return '🍽️'
      case 'attraction': return '🎯'
      case 'transport': return '🚌'
      case 'shopping': return '🛍️'
      default: return '📍'
    }
  }

  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className}`}>
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <RouteIcon className="h-5 w-5 text-blue-600" />
            <h3 className="font-semibold text-gray-900">Route Optimization</h3>
            <Badge variant="secondary">{waypoints.length} stops</Badge>
          </div>
          
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowSettings(!showSettings)}
            >
              <SettingsIcon className="h-4 w-4 mr-1" />
              Settings
            </Button>
            
            <Button
              onClick={handleOptimizeRoute}
              disabled={isOptimizing || waypoints.length < 2}
              size="sm"
            >
              {isOptimizing ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
              ) : (
                <ZapIcon className="h-4 w-4 mr-2" />
              )}
              {isOptimizing ? 'Optimizing...' : 'Optimize Route'}
            </Button>
          </div>
        </div>
      </div>

      {/* Settings Panel */}
      {showSettings && (
        <div className="p-4 bg-gray-50 border-b border-gray-200">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Travel Mode
              </label>
              <select
                value={options.travelMode}
                onChange={(e) => setOptions({ ...options, travelMode: e.target.value as any })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="DRIVING">🚗 Driving</option>
                <option value="WALKING">🚶 Walking</option>
                <option value="TRANSIT">🚌 Public Transit</option>
                <option value="BICYCLING">🚴 Bicycling</option>
              </select>
            </div>

            <div className="space-y-3">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={options.optimizeOrder}
                  onChange={(e) => setOptions({ ...options, optimizeOrder: e.target.checked })}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="ml-2 text-sm text-gray-700">Optimize stop order</span>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={options.avoidTolls}
                  onChange={(e) => setOptions({ ...options, avoidTolls: e.target.checked })}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="ml-2 text-sm text-gray-700">Avoid tolls</span>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={options.avoidHighways}
                  onChange={(e) => setOptions({ ...options, avoidHighways: e.target.checked })}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="ml-2 text-sm text-gray-700">Avoid highways</span>
              </label>
            </div>
          </div>
        </div>
      )}

      {/* Route Summary */}
      {optimizedRoute && (
        <div className="p-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div className="flex items-center space-x-2">
              <ClockIcon className="h-4 w-4 text-gray-500" />
              <div>
                <p className="text-sm text-gray-600">Travel Time</p>
                <p className="font-semibold">{routeOptimizationService.formatDuration(optimizedRoute.totalDuration)}</p>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <NavigationIcon className="h-4 w-4 text-gray-500" />
              <div>
                <p className="text-sm text-gray-600">Distance</p>
                <p className="font-semibold">{routeOptimizationService.formatDistance(optimizedRoute.totalDistance)}</p>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <DollarSignIcon className="h-4 w-4 text-gray-500" />
              <div>
                <p className="text-sm text-gray-600">Est. Cost</p>
                <p className="font-semibold">
                  ${(optimizedRoute.estimatedCost.fuel + optimizedRoute.estimatedCost.tolls + optimizedRoute.estimatedCost.parking).toFixed(2)}
                </p>
              </div>
            </div>
          </div>

          {/* Route Steps */}
          <div className="space-y-2">
            <h4 className="font-medium text-gray-900 mb-3">Optimized Route</h4>
            {optimizedRoute.waypoints.map((waypoint, index) => (
              <div key={waypoint.id} className="flex items-center space-x-3 p-2 bg-gray-50 rounded-lg">
                <div className="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs font-semibold">
                  {index + 1}
                </div>
                <div className="flex-1">
                  <div className="flex items-center space-x-2">
                    <span className="text-lg">{getCategoryIcon(waypoint.category)}</span>
                    <span className="font-medium text-gray-900">{waypoint.name}</span>
                    <Badge variant={waypoint.priority === 'high' ? 'destructive' : waypoint.priority === 'medium' ? 'default' : 'secondary'}>
                      {waypoint.priority}
                    </Badge>
                  </div>
                  {waypoint.estimatedDuration && (
                    <p className="text-sm text-gray-600">Est. {waypoint.estimatedDuration} min visit</p>
                  )}
                </div>
                {index < optimizedRoute.segments.length && (
                  <div className="text-sm text-gray-500">
                    {routeOptimizationService.formatDuration(optimizedRoute.segments[index].duration)} →
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* Cost Breakdown */}
          {options.travelMode === 'DRIVING' && (
            <div className="mt-4 p-3 bg-blue-50 rounded-lg">
              <h5 className="font-medium text-blue-900 mb-2">Cost Breakdown</h5>
              <div className="grid grid-cols-3 gap-4 text-sm">
                <div>
                  <p className="text-blue-700">Fuel</p>
                  <p className="font-semibold text-blue-900">${optimizedRoute.estimatedCost.fuel.toFixed(2)}</p>
                </div>
                <div>
                  <p className="text-blue-700">Tolls</p>
                  <p className="font-semibold text-blue-900">${optimizedRoute.estimatedCost.tolls.toFixed(2)}</p>
                </div>
                <div>
                  <p className="text-blue-700">Parking</p>
                  <p className="font-semibold text-blue-900">${optimizedRoute.estimatedCost.parking.toFixed(2)}</p>
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Empty State */}
      {waypoints.length < 2 && (
        <div className="p-8 text-center text-gray-500">
          <MapPinIcon className="h-12 w-12 mx-auto mb-4 text-gray-300" />
          <p>Add at least 2 destinations to optimize your route</p>
        </div>
      )}
    </div>
  )
}
