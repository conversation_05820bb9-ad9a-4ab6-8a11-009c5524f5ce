{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/lib/supabase/server.ts"], "sourcesContent": ["import { createServerClient } from '@supabase/ssr'\nimport { cookies } from 'next/headers'\n\nexport async function createClient() {\n  const cookieStore = await cookies()\n\n  return createServerClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\n    {\n      cookies: {\n        getAll() {\n          return cookieStore.getAll()\n        },\n        setAll(cookiesToSet) {\n          try {\n            cookiesToSet.forEach(({ name, value, options }) =>\n              cookieStore.set(name, value, options)\n            )\n          } catch {\n            // The `setAll` method was called from a Server Component.\n            // This can be ignored if you have middleware refreshing\n            // user sessions.\n          }\n        },\n      },\n    }\n  )\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;;;AAEO,eAAe;IACpB,MAAM,cAAc,MAAM,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAEhC,OAAO,CAAA,GAAA,2KAAA,CAAA,qBAAkB,AAAD,sUAGtB;QACE,SAAS;YACP;gBACE,OAAO,YAAY,MAAM;YAC3B;YACA,QAAO,YAAY;gBACjB,IAAI;oBACF,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAC5C,YAAY,GAAG,CAAC,MAAM,OAAO;gBAEjC,EAAE,OAAM;gBACN,0DAA0D;gBAC1D,wDAAwD;gBACxD,iBAAiB;gBACnB;YACF;QACF;IACF;AAEJ", "debugId": null}}, {"offset": {"line": 181, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/lib/security/rate-limit.ts"], "sourcesContent": ["// Simple in-memory rate limiting\n// In production, you'd want to use Redis or a similar solution\n\ninterface RateLimitEntry {\n  count: number\n  resetTime: number\n}\n\nconst rateLimitStore = new Map<string, RateLimitEntry>()\n\nexport interface RateLimitResult {\n  success: boolean\n  retryAfter?: number\n}\n\nexport async function rateLimit(\n  identifier: string,\n  requests: number = 100,\n  windowMs: string = '1h'\n): Promise<RateLimitResult> {\n  const windowMilliseconds = parseTimeWindow(windowMs)\n  const now = Date.now()\n  const key = `${identifier}:${Math.floor(now / windowMilliseconds)}`\n  \n  const entry = rateLimitStore.get(key)\n  \n  if (!entry) {\n    rateLimitStore.set(key, {\n      count: 1,\n      resetTime: now + windowMilliseconds\n    })\n    \n    // Clean up old entries\n    cleanupOldEntries(now)\n    \n    return { success: true }\n  }\n  \n  if (entry.count >= requests) {\n    const retryAfter = Math.ceil((entry.resetTime - now) / 1000)\n    return { \n      success: false, \n      retryAfter: retryAfter > 0 ? retryAfter : 1 \n    }\n  }\n  \n  entry.count++\n  return { success: true }\n}\n\nfunction parseTimeWindow(window: string): number {\n  const match = window.match(/^(\\d+)([smhd])$/)\n  if (!match) return 3600000 // Default to 1 hour\n  \n  const value = parseInt(match[1])\n  const unit = match[2]\n  \n  switch (unit) {\n    case 's': return value * 1000\n    case 'm': return value * 60 * 1000\n    case 'h': return value * 60 * 60 * 1000\n    case 'd': return value * 24 * 60 * 60 * 1000\n    default: return 3600000\n  }\n}\n\nfunction cleanupOldEntries(now: number) {\n  // Clean up entries older than 24 hours\n  const cutoff = now - (24 * 60 * 60 * 1000)\n  \n  for (const [key, entry] of rateLimitStore.entries()) {\n    if (entry.resetTime < cutoff) {\n      rateLimitStore.delete(key)\n    }\n  }\n}\n\n// Rate limit configurations for different endpoints\nexport const rateLimitConfigs = {\n  // Authentication endpoints\n  auth: { requests: 5, window: '15m' },\n  \n  // API endpoints\n  api: { requests: 100, window: '1h' },\n  \n  // File upload endpoints\n  upload: { requests: 10, window: '1h' },\n  \n  // Recipe import (external API calls)\n  import: { requests: 20, window: '1h' },\n  \n  // Chat/AI endpoints\n  chat: { requests: 50, window: '1h' },\n  \n  // Search endpoints\n  search: { requests: 200, window: '1h' }\n}\n\n// IP-based rate limiting for public endpoints\nexport async function rateLimitByIP(\n  request: Request,\n  config: { requests: number; window: string } = rateLimitConfigs.api\n): Promise<RateLimitResult> {\n  const ip = getClientIP(request)\n  return rateLimit(`ip:${ip}`, config.requests, config.window)\n}\n\n// User-based rate limiting for authenticated endpoints\nexport async function rateLimitByUser(\n  userId: string,\n  config: { requests: number; window: string } = rateLimitConfigs.api\n): Promise<RateLimitResult> {\n  return rateLimit(`user:${userId}`, config.requests, config.window)\n}\n\nfunction getClientIP(request: Request): string {\n  // Try to get IP from various headers\n  const forwarded = request.headers.get('x-forwarded-for')\n  if (forwarded) {\n    return forwarded.split(',')[0].trim()\n  }\n  \n  const realIP = request.headers.get('x-real-ip')\n  if (realIP) {\n    return realIP\n  }\n  \n  const cfConnectingIP = request.headers.get('cf-connecting-ip')\n  if (cfConnectingIP) {\n    return cfConnectingIP\n  }\n  \n  return 'unknown'\n}\n"], "names": [], "mappings": "AAAA,iCAAiC;AACjC,+DAA+D;;;;;;;AAO/D,MAAM,iBAAiB,IAAI;AAOpB,eAAe,UACpB,UAAkB,EAClB,WAAmB,GAAG,EACtB,WAAmB,IAAI;IAEvB,MAAM,qBAAqB,gBAAgB;IAC3C,MAAM,MAAM,KAAK,GAAG;IACpB,MAAM,MAAM,GAAG,WAAW,CAAC,EAAE,KAAK,KAAK,CAAC,MAAM,qBAAqB;IAEnE,MAAM,QAAQ,eAAe,GAAG,CAAC;IAEjC,IAAI,CAAC,OAAO;QACV,eAAe,GAAG,CAAC,KAAK;YACtB,OAAO;YACP,WAAW,MAAM;QACnB;QAEA,uBAAuB;QACvB,kBAAkB;QAElB,OAAO;YAAE,SAAS;QAAK;IACzB;IAEA,IAAI,MAAM,KAAK,IAAI,UAAU;QAC3B,MAAM,aAAa,KAAK,IAAI,CAAC,CAAC,MAAM,SAAS,GAAG,GAAG,IAAI;QACvD,OAAO;YACL,SAAS;YACT,YAAY,aAAa,IAAI,aAAa;QAC5C;IACF;IAEA,MAAM,KAAK;IACX,OAAO;QAAE,SAAS;IAAK;AACzB;AAEA,SAAS,gBAAgB,MAAc;IACrC,MAAM,QAAQ,OAAO,KAAK,CAAC;IAC3B,IAAI,CAAC,OAAO,OAAO,QAAQ,oBAAoB;;IAE/C,MAAM,QAAQ,SAAS,KAAK,CAAC,EAAE;IAC/B,MAAM,OAAO,KAAK,CAAC,EAAE;IAErB,OAAQ;QACN,KAAK;YAAK,OAAO,QAAQ;QACzB,KAAK;YAAK,OAAO,QAAQ,KAAK;QAC9B,KAAK;YAAK,OAAO,QAAQ,KAAK,KAAK;QACnC,KAAK;YAAK,OAAO,QAAQ,KAAK,KAAK,KAAK;QACxC;YAAS,OAAO;IAClB;AACF;AAEA,SAAS,kBAAkB,GAAW;IACpC,uCAAuC;IACvC,MAAM,SAAS,MAAO,KAAK,KAAK,KAAK;IAErC,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,eAAe,OAAO,GAAI;QACnD,IAAI,MAAM,SAAS,GAAG,QAAQ;YAC5B,eAAe,MAAM,CAAC;QACxB;IACF;AACF;AAGO,MAAM,mBAAmB;IAC9B,2BAA2B;IAC3B,MAAM;QAAE,UAAU;QAAG,QAAQ;IAAM;IAEnC,gBAAgB;IAChB,KAAK;QAAE,UAAU;QAAK,QAAQ;IAAK;IAEnC,wBAAwB;IACxB,QAAQ;QAAE,UAAU;QAAI,QAAQ;IAAK;IAErC,qCAAqC;IACrC,QAAQ;QAAE,UAAU;QAAI,QAAQ;IAAK;IAErC,oBAAoB;IACpB,MAAM;QAAE,UAAU;QAAI,QAAQ;IAAK;IAEnC,mBAAmB;IACnB,QAAQ;QAAE,UAAU;QAAK,QAAQ;IAAK;AACxC;AAGO,eAAe,cACpB,OAAgB,EAChB,SAA+C,iBAAiB,GAAG;IAEnE,MAAM,KAAK,YAAY;IACvB,OAAO,UAAU,CAAC,GAAG,EAAE,IAAI,EAAE,OAAO,QAAQ,EAAE,OAAO,MAAM;AAC7D;AAGO,eAAe,gBACpB,MAAc,EACd,SAA+C,iBAAiB,GAAG;IAEnE,OAAO,UAAU,CAAC,KAAK,EAAE,QAAQ,EAAE,OAAO,QAAQ,EAAE,OAAO,MAAM;AACnE;AAEA,SAAS,YAAY,OAAgB;IACnC,qCAAqC;IACrC,MAAM,YAAY,QAAQ,OAAO,CAAC,GAAG,CAAC;IACtC,IAAI,WAAW;QACb,OAAO,UAAU,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI;IACrC;IAEA,MAAM,SAAS,QAAQ,OAAO,CAAC,GAAG,CAAC;IACnC,IAAI,QAAQ;QACV,OAAO;IACT;IAEA,MAAM,iBAAiB,QAAQ,OAAO,CAAC,GAAG,CAAC;IAC3C,IAAI,gBAAgB;QAClB,OAAO;IACT;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 307, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/lib/security/validation.ts"], "sourcesContent": ["import { NextRequest } from 'next/server'\n\nexport interface ValidationSchema {\n  [key: string]: {\n    required?: boolean\n    type?: 'string' | 'number' | 'boolean' | 'array' | 'object'\n    minLength?: number\n    maxLength?: number\n    min?: number\n    max?: number\n    pattern?: RegExp\n    enum?: string[]\n    maxItems?: number\n    custom?: (value: any) => boolean | string\n  }\n}\n\nexport interface ValidationResult {\n  success: boolean\n  errors?: string[]\n  data?: any\n}\n\nexport async function validateInput(\n  request: NextRequest,\n  schemas: { body?: ValidationSchema; query?: ValidationSchema }\n): Promise<ValidationResult> {\n  const errors: string[] = []\n  const validatedData: any = {}\n\n  try {\n    // Validate request body\n    if (schemas.body) {\n      let body: any = {}\n      \n      if (request.method !== 'GET' && request.method !== 'DELETE') {\n        try {\n          body = await request.json()\n        } catch (error) {\n          errors.push('Invalid JSON in request body')\n          return { success: false, errors }\n        }\n      }\n\n      const bodyValidation = validateObject(body, schemas.body, 'body')\n      if (!bodyValidation.success) {\n        errors.push(...bodyValidation.errors!)\n      } else {\n        validatedData.body = bodyValidation.data\n      }\n    }\n\n    // Validate query parameters\n    if (schemas.query) {\n      const searchParams = request.nextUrl.searchParams\n      const query: any = {}\n      \n      for (const [key, value] of searchParams.entries()) {\n        query[key] = value\n      }\n\n      const queryValidation = validateObject(query, schemas.query, 'query')\n      if (!queryValidation.success) {\n        errors.push(...queryValidation.errors!)\n      } else {\n        validatedData.query = queryValidation.data\n      }\n    }\n\n    return {\n      success: errors.length === 0,\n      errors: errors.length > 0 ? errors : undefined,\n      data: validatedData\n    }\n  } catch (error) {\n    console.error('Validation error:', error)\n    return {\n      success: false,\n      errors: ['Validation failed']\n    }\n  }\n}\n\nfunction validateObject(\n  obj: any,\n  schema: ValidationSchema,\n  prefix: string = ''\n): ValidationResult {\n  const errors: string[] = []\n  const validatedData: any = {}\n\n  for (const [key, rules] of Object.entries(schema)) {\n    const fieldPath = prefix ? `${prefix}.${key}` : key\n    const value = obj[key]\n\n    // Check required fields\n    if (rules.required && (value === undefined || value === null || value === '')) {\n      errors.push(`${fieldPath} is required`)\n      continue\n    }\n\n    // Skip validation if field is not required and not provided\n    if (!rules.required && (value === undefined || value === null)) {\n      continue\n    }\n\n    // Type validation\n    if (rules.type) {\n      const typeValidation = validateType(value, rules.type, fieldPath)\n      if (!typeValidation.success) {\n        errors.push(...typeValidation.errors!)\n        continue\n      }\n    }\n\n    // String validations\n    if (rules.type === 'string' && typeof value === 'string') {\n      if (rules.minLength && value.length < rules.minLength) {\n        errors.push(`${fieldPath} must be at least ${rules.minLength} characters`)\n        continue\n      }\n      \n      if (rules.maxLength && value.length > rules.maxLength) {\n        errors.push(`${fieldPath} must be no more than ${rules.maxLength} characters`)\n        continue\n      }\n      \n      if (rules.pattern && !rules.pattern.test(value)) {\n        errors.push(`${fieldPath} format is invalid`)\n        continue\n      }\n      \n      if (rules.enum && !rules.enum.includes(value)) {\n        errors.push(`${fieldPath} must be one of: ${rules.enum.join(', ')}`)\n        continue\n      }\n    }\n\n    // Number validations\n    if (rules.type === 'number' && typeof value === 'number') {\n      if (rules.min !== undefined && value < rules.min) {\n        errors.push(`${fieldPath} must be at least ${rules.min}`)\n        continue\n      }\n      \n      if (rules.max !== undefined && value > rules.max) {\n        errors.push(`${fieldPath} must be no more than ${rules.max}`)\n        continue\n      }\n    }\n\n    // Array validations\n    if (rules.type === 'array' && Array.isArray(value)) {\n      if (rules.maxItems && value.length > rules.maxItems) {\n        errors.push(`${fieldPath} must have no more than ${rules.maxItems} items`)\n        continue\n      }\n    }\n\n    // Custom validation\n    if (rules.custom) {\n      const customResult = rules.custom(value)\n      if (customResult !== true) {\n        errors.push(typeof customResult === 'string' ? customResult : `${fieldPath} is invalid`)\n        continue\n      }\n    }\n\n    // Sanitize and add to validated data\n    validatedData[key] = sanitizeValue(value, rules.type)\n  }\n\n  return {\n    success: errors.length === 0,\n    errors: errors.length > 0 ? errors : undefined,\n    data: validatedData\n  }\n}\n\nfunction validateType(value: any, expectedType: string, fieldPath: string): ValidationResult {\n  switch (expectedType) {\n    case 'string':\n      if (typeof value !== 'string') {\n        return { success: false, errors: [`${fieldPath} must be a string`] }\n      }\n      break\n    \n    case 'number':\n      if (typeof value !== 'number' || isNaN(value)) {\n        return { success: false, errors: [`${fieldPath} must be a number`] }\n      }\n      break\n    \n    case 'boolean':\n      if (typeof value !== 'boolean') {\n        return { success: false, errors: [`${fieldPath} must be a boolean`] }\n      }\n      break\n    \n    case 'array':\n      if (!Array.isArray(value)) {\n        return { success: false, errors: [`${fieldPath} must be an array`] }\n      }\n      break\n    \n    case 'object':\n      if (typeof value !== 'object' || value === null || Array.isArray(value)) {\n        return { success: false, errors: [`${fieldPath} must be an object`] }\n      }\n      break\n  }\n\n  return { success: true }\n}\n\nfunction sanitizeValue(value: any, type?: string): any {\n  if (value === null || value === undefined) {\n    return value\n  }\n\n  switch (type) {\n    case 'string':\n      return typeof value === 'string' \n        ? value.trim().slice(0, 10000) // Limit string length\n        : String(value).slice(0, 10000)\n    \n    case 'number':\n      return typeof value === 'number' ? value : Number(value)\n    \n    case 'boolean':\n      return Boolean(value)\n    \n    case 'array':\n      return Array.isArray(value) ? value.slice(0, 1000) : [value] // Limit array size\n    \n    case 'object':\n      return typeof value === 'object' ? value : {}\n    \n    default:\n      return value\n  }\n}\n\n// Common validation patterns\nexport const patterns = {\n  email: /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/,\n  url: /^https?:\\/\\/.+/,\n  uuid: /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i,\n  slug: /^[a-z0-9]+(?:-[a-z0-9]+)*$/,\n  phone: /^\\+?[\\d\\s\\-\\(\\)]+$/,\n  date: /^\\d{4}-\\d{2}-\\d{2}$/,\n  time: /^\\d{2}:\\d{2}$/,\n  datetime: /^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}/\n}\n\n// Validation helpers for common use cases\nexport function validateEmail(email: string): boolean {\n  return patterns.email.test(email)\n}\n\nexport function validateURL(url: string): boolean {\n  return patterns.url.test(url)\n}\n\nexport function validateUUID(uuid: string): boolean {\n  return patterns.uuid.test(uuid)\n}\n\nexport function sanitizeHTML(input: string): string {\n  return input\n    .replace(/</g, '&lt;')\n    .replace(/>/g, '&gt;')\n    .replace(/\"/g, '&quot;')\n    .replace(/'/g, '&#x27;')\n    .replace(/\\//g, '&#x2F;')\n}\n\nexport function validateAndSanitizeInput(\n  input: any,\n  schema: ValidationSchema\n): { isValid: boolean; sanitized: any; errors: string[] } {\n  const validation = validateObject(input, schema)\n  \n  return {\n    isValid: validation.success,\n    sanitized: validation.data || {},\n    errors: validation.errors || []\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;AAuBO,eAAe,cACpB,OAAoB,EACpB,OAA8D;IAE9D,MAAM,SAAmB,EAAE;IAC3B,MAAM,gBAAqB,CAAC;IAE5B,IAAI;QACF,wBAAwB;QACxB,IAAI,QAAQ,IAAI,EAAE;YAChB,IAAI,OAAY,CAAC;YAEjB,IAAI,QAAQ,MAAM,KAAK,SAAS,QAAQ,MAAM,KAAK,UAAU;gBAC3D,IAAI;oBACF,OAAO,MAAM,QAAQ,IAAI;gBAC3B,EAAE,OAAO,OAAO;oBACd,OAAO,IAAI,CAAC;oBACZ,OAAO;wBAAE,SAAS;wBAAO;oBAAO;gBAClC;YACF;YAEA,MAAM,iBAAiB,eAAe,MAAM,QAAQ,IAAI,EAAE;YAC1D,IAAI,CAAC,eAAe,OAAO,EAAE;gBAC3B,OAAO,IAAI,IAAI,eAAe,MAAM;YACtC,OAAO;gBACL,cAAc,IAAI,GAAG,eAAe,IAAI;YAC1C;QACF;QAEA,4BAA4B;QAC5B,IAAI,QAAQ,KAAK,EAAE;YACjB,MAAM,eAAe,QAAQ,OAAO,CAAC,YAAY;YACjD,MAAM,QAAa,CAAC;YAEpB,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,aAAa,OAAO,GAAI;gBACjD,KAAK,CAAC,IAAI,GAAG;YACf;YAEA,MAAM,kBAAkB,eAAe,OAAO,QAAQ,KAAK,EAAE;YAC7D,IAAI,CAAC,gBAAgB,OAAO,EAAE;gBAC5B,OAAO,IAAI,IAAI,gBAAgB,MAAM;YACvC,OAAO;gBACL,cAAc,KAAK,GAAG,gBAAgB,IAAI;YAC5C;QACF;QAEA,OAAO;YACL,SAAS,OAAO,MAAM,KAAK;YAC3B,QAAQ,OAAO,MAAM,GAAG,IAAI,SAAS;YACrC,MAAM;QACR;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QACnC,OAAO;YACL,SAAS;YACT,QAAQ;gBAAC;aAAoB;QAC/B;IACF;AACF;AAEA,SAAS,eACP,GAAQ,EACR,MAAwB,EACxB,SAAiB,EAAE;IAEnB,MAAM,SAAmB,EAAE;IAC3B,MAAM,gBAAqB,CAAC;IAE5B,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,QAAS;QACjD,MAAM,YAAY,SAAS,GAAG,OAAO,CAAC,EAAE,KAAK,GAAG;QAChD,MAAM,QAAQ,GAAG,CAAC,IAAI;QAEtB,wBAAwB;QACxB,IAAI,MAAM,QAAQ,IAAI,CAAC,UAAU,aAAa,UAAU,QAAQ,UAAU,EAAE,GAAG;YAC7E,OAAO,IAAI,CAAC,GAAG,UAAU,YAAY,CAAC;YACtC;QACF;QAEA,4DAA4D;QAC5D,IAAI,CAAC,MAAM,QAAQ,IAAI,CAAC,UAAU,aAAa,UAAU,IAAI,GAAG;YAC9D;QACF;QAEA,kBAAkB;QAClB,IAAI,MAAM,IAAI,EAAE;YACd,MAAM,iBAAiB,aAAa,OAAO,MAAM,IAAI,EAAE;YACvD,IAAI,CAAC,eAAe,OAAO,EAAE;gBAC3B,OAAO,IAAI,IAAI,eAAe,MAAM;gBACpC;YACF;QACF;QAEA,qBAAqB;QACrB,IAAI,MAAM,IAAI,KAAK,YAAY,OAAO,UAAU,UAAU;YACxD,IAAI,MAAM,SAAS,IAAI,MAAM,MAAM,GAAG,MAAM,SAAS,EAAE;gBACrD,OAAO,IAAI,CAAC,GAAG,UAAU,kBAAkB,EAAE,MAAM,SAAS,CAAC,WAAW,CAAC;gBACzE;YACF;YAEA,IAAI,MAAM,SAAS,IAAI,MAAM,MAAM,GAAG,MAAM,SAAS,EAAE;gBACrD,OAAO,IAAI,CAAC,GAAG,UAAU,sBAAsB,EAAE,MAAM,SAAS,CAAC,WAAW,CAAC;gBAC7E;YACF;YAEA,IAAI,MAAM,OAAO,IAAI,CAAC,MAAM,OAAO,CAAC,IAAI,CAAC,QAAQ;gBAC/C,OAAO,IAAI,CAAC,GAAG,UAAU,kBAAkB,CAAC;gBAC5C;YACF;YAEA,IAAI,MAAM,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,QAAQ,CAAC,QAAQ;gBAC7C,OAAO,IAAI,CAAC,GAAG,UAAU,iBAAiB,EAAE,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO;gBACnE;YACF;QACF;QAEA,qBAAqB;QACrB,IAAI,MAAM,IAAI,KAAK,YAAY,OAAO,UAAU,UAAU;YACxD,IAAI,MAAM,GAAG,KAAK,aAAa,QAAQ,MAAM,GAAG,EAAE;gBAChD,OAAO,IAAI,CAAC,GAAG,UAAU,kBAAkB,EAAE,MAAM,GAAG,EAAE;gBACxD;YACF;YAEA,IAAI,MAAM,GAAG,KAAK,aAAa,QAAQ,MAAM,GAAG,EAAE;gBAChD,OAAO,IAAI,CAAC,GAAG,UAAU,sBAAsB,EAAE,MAAM,GAAG,EAAE;gBAC5D;YACF;QACF;QAEA,oBAAoB;QACpB,IAAI,MAAM,IAAI,KAAK,WAAW,MAAM,OAAO,CAAC,QAAQ;YAClD,IAAI,MAAM,QAAQ,IAAI,MAAM,MAAM,GAAG,MAAM,QAAQ,EAAE;gBACnD,OAAO,IAAI,CAAC,GAAG,UAAU,wBAAwB,EAAE,MAAM,QAAQ,CAAC,MAAM,CAAC;gBACzE;YACF;QACF;QAEA,oBAAoB;QACpB,IAAI,MAAM,MAAM,EAAE;YAChB,MAAM,eAAe,MAAM,MAAM,CAAC;YAClC,IAAI,iBAAiB,MAAM;gBACzB,OAAO,IAAI,CAAC,OAAO,iBAAiB,WAAW,eAAe,GAAG,UAAU,WAAW,CAAC;gBACvF;YACF;QACF;QAEA,qCAAqC;QACrC,aAAa,CAAC,IAAI,GAAG,cAAc,OAAO,MAAM,IAAI;IACtD;IAEA,OAAO;QACL,SAAS,OAAO,MAAM,KAAK;QAC3B,QAAQ,OAAO,MAAM,GAAG,IAAI,SAAS;QACrC,MAAM;IACR;AACF;AAEA,SAAS,aAAa,KAAU,EAAE,YAAoB,EAAE,SAAiB;IACvE,OAAQ;QACN,KAAK;YACH,IAAI,OAAO,UAAU,UAAU;gBAC7B,OAAO;oBAAE,SAAS;oBAAO,QAAQ;wBAAC,GAAG,UAAU,iBAAiB,CAAC;qBAAC;gBAAC;YACrE;YACA;QAEF,KAAK;YACH,IAAI,OAAO,UAAU,YAAY,MAAM,QAAQ;gBAC7C,OAAO;oBAAE,SAAS;oBAAO,QAAQ;wBAAC,GAAG,UAAU,iBAAiB,CAAC;qBAAC;gBAAC;YACrE;YACA;QAEF,KAAK;YACH,IAAI,OAAO,UAAU,WAAW;gBAC9B,OAAO;oBAAE,SAAS;oBAAO,QAAQ;wBAAC,GAAG,UAAU,kBAAkB,CAAC;qBAAC;gBAAC;YACtE;YACA;QAEF,KAAK;YACH,IAAI,CAAC,MAAM,OAAO,CAAC,QAAQ;gBACzB,OAAO;oBAAE,SAAS;oBAAO,QAAQ;wBAAC,GAAG,UAAU,iBAAiB,CAAC;qBAAC;gBAAC;YACrE;YACA;QAEF,KAAK;YACH,IAAI,OAAO,UAAU,YAAY,UAAU,QAAQ,MAAM,OAAO,CAAC,QAAQ;gBACvE,OAAO;oBAAE,SAAS;oBAAO,QAAQ;wBAAC,GAAG,UAAU,kBAAkB,CAAC;qBAAC;gBAAC;YACtE;YACA;IACJ;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAEA,SAAS,cAAc,KAAU,EAAE,IAAa;IAC9C,IAAI,UAAU,QAAQ,UAAU,WAAW;QACzC,OAAO;IACT;IAEA,OAAQ;QACN,KAAK;YACH,OAAO,OAAO,UAAU,WACpB,MAAM,IAAI,GAAG,KAAK,CAAC,GAAG,OAAO,sBAAsB;eACnD,OAAO,OAAO,KAAK,CAAC,GAAG;QAE7B,KAAK;YACH,OAAO,OAAO,UAAU,WAAW,QAAQ,OAAO;QAEpD,KAAK;YACH,OAAO,QAAQ;QAEjB,KAAK;YACH,OAAO,MAAM,OAAO,CAAC,SAAS,MAAM,KAAK,CAAC,GAAG,QAAQ;gBAAC;aAAM,CAAC,mBAAmB;;QAElF,KAAK;YACH,OAAO,OAAO,UAAU,WAAW,QAAQ,CAAC;QAE9C;YACE,OAAO;IACX;AACF;AAGO,MAAM,WAAW;IACtB,OAAO;IACP,KAAK;IACL,MAAM;IACN,MAAM;IACN,OAAO;IACP,MAAM;IACN,MAAM;IACN,UAAU;AACZ;AAGO,SAAS,cAAc,KAAa;IACzC,OAAO,SAAS,KAAK,CAAC,IAAI,CAAC;AAC7B;AAEO,SAAS,YAAY,GAAW;IACrC,OAAO,SAAS,GAAG,CAAC,IAAI,CAAC;AAC3B;AAEO,SAAS,aAAa,IAAY;IACvC,OAAO,SAAS,IAAI,CAAC,IAAI,CAAC;AAC5B;AAEO,SAAS,aAAa,KAAa;IACxC,OAAO,MACJ,OAAO,CAAC,MAAM,QACd,OAAO,CAAC,MAAM,QACd,OAAO,CAAC,MAAM,UACd,OAAO,CAAC,MAAM,UACd,OAAO,CAAC,OAAO;AACpB;AAEO,SAAS,yBACd,KAAU,EACV,MAAwB;IAExB,MAAM,aAAa,eAAe,OAAO;IAEzC,OAAO;QACL,SAAS,WAAW,OAAO;QAC3B,WAAW,WAAW,IAAI,IAAI,CAAC;QAC/B,QAAQ,WAAW,MAAM,IAAI,EAAE;IACjC;AACF", "debugId": null}}, {"offset": {"line": 563, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/lib/security/error-handler.ts"], "sourcesContent": ["import { NextResponse } from 'next/server'\n\nexport interface SecureError {\n  code: string\n  message: string\n  statusCode: number\n}\n\n// Define secure error mappings\nconst ERROR_MAPPINGS: Record<string, SecureError> = {\n  // Authentication errors\n  'PGRST301': { code: 'AUTH_REQUIRED', message: 'Authentication required', statusCode: 401 },\n  'PGRST302': { code: 'FORBIDDEN', message: 'Access denied', statusCode: 403 },\n  \n  // Database constraint errors\n  '23503': { code: 'INVALID_REFERENCE', message: 'Invalid data reference', statusCode: 400 },\n  '23505': { code: 'DUPLICATE_ENTRY', message: 'Duplicate entry not allowed', statusCode: 409 },\n  '23514': { code: 'VALIDATION_ERROR', message: 'Data validation failed', statusCode: 400 },\n  \n  // Rate limiting\n  'RATE_LIMIT': { code: 'RATE_LIMIT', message: 'Too many requests', statusCode: 429 },\n  \n  // Validation errors\n  'VALIDATION_ERROR': { code: 'VALIDATION_ERROR', message: 'Invalid input data', statusCode: 400 },\n  \n  // Generic errors\n  'INTERNAL_ERROR': { code: 'INTERNAL_ERROR', message: 'An unexpected error occurred', statusCode: 500 },\n}\n\nexport function handleSecureError(error: any): NextResponse {\n  // Log the full error for debugging (server-side only)\n  console.error('Secure Error Handler:', {\n    message: error.message,\n    code: error.code,\n    details: error.details,\n    timestamp: new Date().toISOString(),\n    stack: error.stack\n  })\n\n  // Determine the error type\n  let secureError: SecureError\n\n  if (error.code && ERROR_MAPPINGS[error.code]) {\n    secureError = ERROR_MAPPINGS[error.code]\n  } else if (error.message?.includes('rate limit')) {\n    secureError = ERROR_MAPPINGS['RATE_LIMIT']\n  } else if (error.message?.includes('validation')) {\n    secureError = ERROR_MAPPINGS['VALIDATION_ERROR']\n  } else {\n    secureError = ERROR_MAPPINGS['INTERNAL_ERROR']\n  }\n\n  // Return sanitized error response\n  return NextResponse.json(\n    {\n      error: {\n        code: secureError.code,\n        message: secureError.message\n      }\n    },\n    { status: secureError.statusCode }\n  )\n}\n\nexport function createValidationError(message: string): Error {\n  const error = new Error(message)\n  error.name = 'VALIDATION_ERROR'\n  return error\n}\n\nexport function createAuthError(message: string = 'Authentication required'): Error {\n  const error = new Error(message)\n  error.name = 'AUTH_REQUIRED'\n  return error\n}\n\nexport function createForbiddenError(message: string = 'Access denied'): Error {\n  const error = new Error(message)\n  error.name = 'FORBIDDEN'\n  return error\n}\n"], "names": [], "mappings": ";;;;;;AAAA;;AAQA,+BAA+B;AAC/B,MAAM,iBAA8C;IAClD,wBAAwB;IACxB,YAAY;QAAE,MAAM;QAAiB,SAAS;QAA2B,YAAY;IAAI;IACzF,YAAY;QAAE,MAAM;QAAa,SAAS;QAAiB,YAAY;IAAI;IAE3E,6BAA6B;IAC7B,SAAS;QAAE,MAAM;QAAqB,SAAS;QAA0B,YAAY;IAAI;IACzF,SAAS;QAAE,MAAM;QAAmB,SAAS;QAA+B,YAAY;IAAI;IAC5F,SAAS;QAAE,MAAM;QAAoB,SAAS;QAA0B,YAAY;IAAI;IAExF,gBAAgB;IAChB,cAAc;QAAE,MAAM;QAAc,SAAS;QAAqB,YAAY;IAAI;IAElF,oBAAoB;IACpB,oBAAoB;QAAE,MAAM;QAAoB,SAAS;QAAsB,YAAY;IAAI;IAE/F,iBAAiB;IACjB,kBAAkB;QAAE,MAAM;QAAkB,SAAS;QAAgC,YAAY;IAAI;AACvG;AAEO,SAAS,kBAAkB,KAAU;IAC1C,sDAAsD;IACtD,QAAQ,KAAK,CAAC,yBAAyB;QACrC,SAAS,MAAM,OAAO;QACtB,MAAM,MAAM,IAAI;QAChB,SAAS,MAAM,OAAO;QACtB,WAAW,IAAI,OAAO,WAAW;QACjC,OAAO,MAAM,KAAK;IACpB;IAEA,2BAA2B;IAC3B,IAAI;IAEJ,IAAI,MAAM,IAAI,IAAI,cAAc,CAAC,MAAM,IAAI,CAAC,EAAE;QAC5C,cAAc,cAAc,CAAC,MAAM,IAAI,CAAC;IAC1C,OAAO,IAAI,MAAM,OAAO,EAAE,SAAS,eAAe;QAChD,cAAc,cAAc,CAAC,aAAa;IAC5C,OAAO,IAAI,MAAM,OAAO,EAAE,SAAS,eAAe;QAChD,cAAc,cAAc,CAAC,mBAAmB;IAClD,OAAO;QACL,cAAc,cAAc,CAAC,iBAAiB;IAChD;IAEA,kCAAkC;IAClC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;QACE,OAAO;YACL,MAAM,YAAY,IAAI;YACtB,SAAS,YAAY,OAAO;QAC9B;IACF,GACA;QAAE,QAAQ,YAAY,UAAU;IAAC;AAErC;AAEO,SAAS,sBAAsB,OAAe;IACnD,MAAM,QAAQ,IAAI,MAAM;IACxB,MAAM,IAAI,GAAG;IACb,OAAO;AACT;AAEO,SAAS,gBAAgB,UAAkB,yBAAyB;IACzE,MAAM,QAAQ,IAAI,MAAM;IACxB,MAAM,IAAI,GAAG;IACb,OAAO;AACT;AAEO,SAAS,qBAAqB,UAAkB,eAAe;IACpE,MAAM,QAAQ,IAAI,MAAM;IACxB,MAAM,IAAI,GAAG;IACb,OAAO;AACT", "debugId": null}}, {"offset": {"line": 670, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/lib/security/middleware.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { createClient } from '@/lib/supabase/server'\nimport { rateLimit } from './rate-limit'\nimport { validateInput } from './validation'\nimport { handleSecureError } from './error-handler'\n\nexport interface SecurityOptions {\n  requireAuth?: boolean\n  rateLimit?: {\n    requests: number\n    window: string\n  }\n  validation?: {\n    body?: any\n    query?: any\n  }\n  auditLog?: boolean\n}\n\nexport function withSecurity(\n  handler: (request: NextRequest, context?: any) => Promise<NextResponse>,\n  options: SecurityOptions = {}\n) {\n  return async (request: NextRequest, context?: any) => {\n    try {\n      const supabase = await createClient()\n      \n      // Authentication check\n      if (options.requireAuth !== false) {\n        const { data: { user }, error } = await supabase.auth.getUser()\n        if (error || !user) {\n          return NextResponse.json({\n            error: {\n              code: 'AUTH_REQUIRED',\n              message: 'Authentication required'\n            }\n          }, { status: 401 })\n        }\n\n        // Add user to context\n        if (context) {\n          context.user = user\n        }\n      }\n\n      // Rate limiting\n      if (options.rateLimit) {\n        const clientIP = request.ip || \n          request.headers.get('x-forwarded-for') || \n          request.headers.get('x-real-ip') || \n          'unknown'\n        \n        const rateLimitResult = await rateLimit(\n          clientIP,\n          options.rateLimit.requests,\n          options.rateLimit.window\n        )\n        \n        if (!rateLimitResult.success) {\n          return NextResponse.json(\n            {\n              error: {\n                code: 'RATE_LIMIT',\n                message: 'Too many requests',\n                retryAfter: rateLimitResult.retryAfter\n              }\n            },\n            {\n              status: 429,\n              headers: {\n                'Retry-After': rateLimitResult.retryAfter?.toString() || '60'\n              }\n            }\n          )\n        }\n      }\n\n      // Input validation\n      if (options.validation) {\n        const validationResult = await validateInput(request, options.validation)\n        if (!validationResult.success) {\n          return NextResponse.json(\n            {\n              error: {\n                code: 'VALIDATION_ERROR',\n                message: 'Invalid input data',\n                details: validationResult.errors\n              }\n            },\n            { status: 400 }\n          )\n        }\n\n        // Add validated data to context to avoid re-reading body\n        if (context && validationResult.data) {\n          context.validatedData = validationResult.data\n        }\n      }\n\n      // Execute the handler\n      const response = await handler(request, context)\n\n      // Audit logging\n      if (options.auditLog && context?.user) {\n        await logAuditEvent(request, context.user, response)\n      }\n\n      return response\n    } catch (error) {\n      return handleSecureError(error)\n    }\n  }\n}\n\nasync function logAuditEvent(\n  request: NextRequest,\n  user: any,\n  response: NextResponse\n) {\n  try {\n    const supabase = await createClient()\n    \n    const action = `${request.method} ${request.nextUrl.pathname}`\n    const clientIP = request.ip || \n      request.headers.get('x-forwarded-for') || \n      request.headers.get('x-real-ip')\n    \n    await supabase\n      .from('audit_logs')\n      .insert([\n        {\n          user_id: user.id,\n          action,\n          ip_address: clientIP,\n          user_agent: request.headers.get('user-agent'),\n          new_values: {\n            status: response.status,\n            method: request.method,\n            path: request.nextUrl.pathname,\n            timestamp: new Date().toISOString()\n          }\n        }\n      ])\n  } catch (error) {\n    console.error('Failed to log audit event:', error)\n  }\n}\n\n\n\n\n\n// Common validation schemas\nexport const commonSchemas = {\n  task: {\n    title: { required: true, type: 'string', maxLength: 200 },\n    description: { required: false, type: 'string', maxLength: 1000 },\n    priority: { required: false, type: 'string', enum: ['low', 'medium', 'high'] },\n    due_date: { required: false, type: 'string' },\n    category: { required: false, type: 'string', maxLength: 100 },\n    estimated_duration: { required: false, type: 'number', min: 1, max: 1440 }\n  },\n  recipe: {\n    title: { required: true, type: 'string', maxLength: 200 },\n    description: { required: false, type: 'string', maxLength: 2000 },\n    ingredients: { required: true, type: 'array', maxItems: 50 },\n    instructions: { required: true, type: 'array', maxItems: 50 },\n    prep_time: { required: false, type: 'number', min: 0, max: 1440 },\n    cook_time: { required: false, type: 'number', min: 0, max: 1440 },\n    servings: { required: false, type: 'number', min: 1, max: 50 },\n    difficulty: { required: false, type: 'string', enum: ['easy', 'medium', 'hard'] }\n  },\n  shoppingListItem: {\n    name: { required: true, type: 'string', maxLength: 200 },\n    quantity: { required: false, type: 'number', min: 0 },\n    unit: { required: false, type: 'string', maxLength: 50 },\n    category: { required: false, type: 'string', maxLength: 100 },\n    priority: { required: false, type: 'number', min: 1, max: 5 }\n  },\n  transaction: {\n    amount: { required: true, type: 'number' },\n    description: { required: true, type: 'string', maxLength: 200 },\n    category_id: { required: true, type: 'string' },\n    transaction_type: { required: true, type: 'string', enum: ['income', 'expense'] },\n    date: { required: true, type: 'string' }\n  }\n}\n\n\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAeO,SAAS,aACd,OAAuE,EACvE,UAA2B,CAAC,CAAC;IAE7B,OAAO,OAAO,SAAsB;QAClC,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,kIAAA,CAAA,eAAY,AAAD;YAElC,uBAAuB;YACvB,IAAI,QAAQ,WAAW,KAAK,OAAO;gBACjC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;gBAC7D,IAAI,SAAS,CAAC,MAAM;oBAClB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;wBACvB,OAAO;4BACL,MAAM;4BACN,SAAS;wBACX;oBACF,GAAG;wBAAE,QAAQ;oBAAI;gBACnB;gBAEA,sBAAsB;gBACtB,IAAI,SAAS;oBACX,QAAQ,IAAI,GAAG;gBACjB;YACF;YAEA,gBAAgB;YAChB,IAAI,QAAQ,SAAS,EAAE;gBACrB,MAAM,WAAW,QAAQ,EAAE,IACzB,QAAQ,OAAO,CAAC,GAAG,CAAC,sBACpB,QAAQ,OAAO,CAAC,GAAG,CAAC,gBACpB;gBAEF,MAAM,kBAAkB,MAAM,CAAA,GAAA,yIAAA,CAAA,YAAS,AAAD,EACpC,UACA,QAAQ,SAAS,CAAC,QAAQ,EAC1B,QAAQ,SAAS,CAAC,MAAM;gBAG1B,IAAI,CAAC,gBAAgB,OAAO,EAAE;oBAC5B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;wBACE,OAAO;4BACL,MAAM;4BACN,SAAS;4BACT,YAAY,gBAAgB,UAAU;wBACxC;oBACF,GACA;wBACE,QAAQ;wBACR,SAAS;4BACP,eAAe,gBAAgB,UAAU,EAAE,cAAc;wBAC3D;oBACF;gBAEJ;YACF;YAEA,mBAAmB;YACnB,IAAI,QAAQ,UAAU,EAAE;gBACtB,MAAM,mBAAmB,MAAM,CAAA,GAAA,sIAAA,CAAA,gBAAa,AAAD,EAAE,SAAS,QAAQ,UAAU;gBACxE,IAAI,CAAC,iBAAiB,OAAO,EAAE;oBAC7B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;wBACE,OAAO;4BACL,MAAM;4BACN,SAAS;4BACT,SAAS,iBAAiB,MAAM;wBAClC;oBACF,GACA;wBAAE,QAAQ;oBAAI;gBAElB;gBAEA,yDAAyD;gBACzD,IAAI,WAAW,iBAAiB,IAAI,EAAE;oBACpC,QAAQ,aAAa,GAAG,iBAAiB,IAAI;gBAC/C;YACF;YAEA,sBAAsB;YACtB,MAAM,WAAW,MAAM,QAAQ,SAAS;YAExC,gBAAgB;YAChB,IAAI,QAAQ,QAAQ,IAAI,SAAS,MAAM;gBACrC,MAAM,cAAc,SAAS,QAAQ,IAAI,EAAE;YAC7C;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,OAAO,CAAA,GAAA,4IAAA,CAAA,oBAAiB,AAAD,EAAE;QAC3B;IACF;AACF;AAEA,eAAe,cACb,OAAoB,EACpB,IAAS,EACT,QAAsB;IAEtB,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,kIAAA,CAAA,eAAY,AAAD;QAElC,MAAM,SAAS,GAAG,QAAQ,MAAM,CAAC,CAAC,EAAE,QAAQ,OAAO,CAAC,QAAQ,EAAE;QAC9D,MAAM,WAAW,QAAQ,EAAE,IACzB,QAAQ,OAAO,CAAC,GAAG,CAAC,sBACpB,QAAQ,OAAO,CAAC,GAAG,CAAC;QAEtB,MAAM,SACH,IAAI,CAAC,cACL,MAAM,CAAC;YACN;gBACE,SAAS,KAAK,EAAE;gBAChB;gBACA,YAAY;gBACZ,YAAY,QAAQ,OAAO,CAAC,GAAG,CAAC;gBAChC,YAAY;oBACV,QAAQ,SAAS,MAAM;oBACvB,QAAQ,QAAQ,MAAM;oBACtB,MAAM,QAAQ,OAAO,CAAC,QAAQ;oBAC9B,WAAW,IAAI,OAAO,WAAW;gBACnC;YACF;SACD;IACL,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;IAC9C;AACF;AAOO,MAAM,gBAAgB;IAC3B,MAAM;QACJ,OAAO;YAAE,UAAU;YAAM,MAAM;YAAU,WAAW;QAAI;QACxD,aAAa;YAAE,UAAU;YAAO,MAAM;YAAU,WAAW;QAAK;QAChE,UAAU;YAAE,UAAU;YAAO,MAAM;YAAU,MAAM;gBAAC;gBAAO;gBAAU;aAAO;QAAC;QAC7E,UAAU;YAAE,UAAU;YAAO,MAAM;QAAS;QAC5C,UAAU;YAAE,UAAU;YAAO,MAAM;YAAU,WAAW;QAAI;QAC5D,oBAAoB;YAAE,UAAU;YAAO,MAAM;YAAU,KAAK;YAAG,KAAK;QAAK;IAC3E;IACA,QAAQ;QACN,OAAO;YAAE,UAAU;YAAM,MAAM;YAAU,WAAW;QAAI;QACxD,aAAa;YAAE,UAAU;YAAO,MAAM;YAAU,WAAW;QAAK;QAChE,aAAa;YAAE,UAAU;YAAM,MAAM;YAAS,UAAU;QAAG;QAC3D,cAAc;YAAE,UAAU;YAAM,MAAM;YAAS,UAAU;QAAG;QAC5D,WAAW;YAAE,UAAU;YAAO,MAAM;YAAU,KAAK;YAAG,KAAK;QAAK;QAChE,WAAW;YAAE,UAAU;YAAO,MAAM;YAAU,KAAK;YAAG,KAAK;QAAK;QAChE,UAAU;YAAE,UAAU;YAAO,MAAM;YAAU,KAAK;YAAG,KAAK;QAAG;QAC7D,YAAY;YAAE,UAAU;YAAO,MAAM;YAAU,MAAM;gBAAC;gBAAQ;gBAAU;aAAO;QAAC;IAClF;IACA,kBAAkB;QAChB,MAAM;YAAE,UAAU;YAAM,MAAM;YAAU,WAAW;QAAI;QACvD,UAAU;YAAE,UAAU;YAAO,MAAM;YAAU,KAAK;QAAE;QACpD,MAAM;YAAE,UAAU;YAAO,MAAM;YAAU,WAAW;QAAG;QACvD,UAAU;YAAE,UAAU;YAAO,MAAM;YAAU,WAAW;QAAI;QAC5D,UAAU;YAAE,UAAU;YAAO,MAAM;YAAU,KAAK;YAAG,KAAK;QAAE;IAC9D;IACA,aAAa;QACX,QAAQ;YAAE,UAAU;YAAM,MAAM;QAAS;QACzC,aAAa;YAAE,UAAU;YAAM,MAAM;YAAU,WAAW;QAAI;QAC9D,aAAa;YAAE,UAAU;YAAM,MAAM;QAAS;QAC9C,kBAAkB;YAAE,UAAU;YAAM,MAAM;YAAU,MAAM;gBAAC;gBAAU;aAAU;QAAC;QAChF,MAAM;YAAE,UAAU;YAAM,MAAM;QAAS;IACzC;AACF", "debugId": null}}, {"offset": {"line": 927, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/lib/security/utils.ts"], "sourcesContent": ["import { NextResponse } from 'next/server'\n\n// Security headers helper\nexport function addSecurityHeaders(response: NextResponse): NextResponse {\n  // Security headers\n  response.headers.set('X-Content-Type-Options', 'nosniff')\n  response.headers.set('X-Frame-Options', 'DENY')\n  response.headers.set('X-XSS-Protection', '1; mode=block')\n  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin')\n  response.headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=()')\n  response.headers.set(\n    'Content-Security-Policy',\n    \"default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:;\"\n  )\n  \n  // Remove server information\n  response.headers.delete('Server')\n  response.headers.delete('X-Powered-By')\n  \n  return response\n}\n\n// Input sanitization helpers\nexport function sanitizeString(input: string): string {\n  if (!input || typeof input !== 'string') return ''\n  \n  // Remove potentially dangerous characters\n  return input\n    .replace(/[<>]/g, '') // Remove HTML tags\n    .replace(/javascript:/gi, '') // Remove javascript: protocol\n    .replace(/on\\w+=/gi, '') // Remove event handlers\n    .trim()\n    .slice(0, 10000) // Limit length\n}\n\nexport function sanitizeNumber(input: any): number | null {\n  const num = Number(input)\n  if (isNaN(num) || !isFinite(num)) return null\n  return num\n}\n\nexport function sanitizeArray(input: any): any[] {\n  if (!Array.isArray(input)) return []\n  return input.slice(0, 100) // Limit array size\n}\n\nexport function sanitizeObject(input: any, allowedKeys: string[]): any {\n  if (typeof input !== 'object' || input === null) return {}\n  \n  const sanitized: any = {}\n  for (const key of allowedKeys) {\n    if (key in input) {\n      sanitized[key] = input[key]\n    }\n  }\n  return sanitized\n}\n"], "names": [], "mappings": ";;;;;;;AAGO,SAAS,mBAAmB,QAAsB;IACvD,mBAAmB;IACnB,SAAS,OAAO,CAAC,GAAG,CAAC,0BAA0B;IAC/C,SAAS,OAAO,CAAC,GAAG,CAAC,mBAAmB;IACxC,SAAS,OAAO,CAAC,GAAG,CAAC,oBAAoB;IACzC,SAAS,OAAO,CAAC,GAAG,CAAC,mBAAmB;IACxC,SAAS,OAAO,CAAC,GAAG,CAAC,sBAAsB;IAC3C,SAAS,OAAO,CAAC,GAAG,CAClB,2BACA;IAGF,4BAA4B;IAC5B,SAAS,OAAO,CAAC,MAAM,CAAC;IACxB,SAAS,OAAO,CAAC,MAAM,CAAC;IAExB,OAAO;AACT;AAGO,SAAS,eAAe,KAAa;IAC1C,IAAI,CAAC,SAAS,OAAO,UAAU,UAAU,OAAO;IAEhD,0CAA0C;IAC1C,OAAO,MACJ,OAAO,CAAC,SAAS,IAAI,mBAAmB;KACxC,OAAO,CAAC,iBAAiB,IAAI,8BAA8B;KAC3D,OAAO,CAAC,YAAY,IAAI,wBAAwB;KAChD,IAAI,GACJ,KAAK,CAAC,GAAG,OAAO,eAAe;;AACpC;AAEO,SAAS,eAAe,KAAU;IACvC,MAAM,MAAM,OAAO;IACnB,IAAI,MAAM,QAAQ,CAAC,SAAS,MAAM,OAAO;IACzC,OAAO;AACT;AAEO,SAAS,cAAc,KAAU;IACtC,IAAI,CAAC,MAAM,OAAO,CAAC,QAAQ,OAAO,EAAE;IACpC,OAAO,MAAM,KAAK,CAAC,GAAG,KAAK,mBAAmB;;AAChD;AAEO,SAAS,eAAe,KAAU,EAAE,WAAqB;IAC9D,IAAI,OAAO,UAAU,YAAY,UAAU,MAAM,OAAO,CAAC;IAEzD,MAAM,YAAiB,CAAC;IACxB,KAAK,MAAM,OAAO,YAAa;QAC7B,IAAI,OAAO,OAAO;YAChB,SAAS,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI;QAC7B;IACF;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 982, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/lib/auth/profile-manager.ts"], "sourcesContent": ["import { createClient } from '@/lib/supabase/server'\n\nexport interface UserProfile {\n  id: string\n  email: string\n  full_name?: string\n  avatar_url?: string\n  created_at?: string\n  updated_at?: string\n}\n\nexport async function ensureUserProfile(userId: string, email: string, fullName?: string): Promise<UserProfile> {\n  const supabase = await createClient()\n\n  try {\n    // First, check if profile already exists\n    const { data: existingProfile, error: fetchError } = await supabase\n      .from('profiles')\n      .select('*')\n      .eq('id', userId)\n      .single()\n\n    if (existingProfile && !fetchError) {\n      return existingProfile\n    }\n\n    // If profile doesn't exist, create it\n    const { data: newProfile, error: insertError } = await supabase\n      .from('profiles')\n      .insert([\n        {\n          id: userId,\n          email: email,\n          full_name: fullName || email.split('@')[0],\n          created_at: new Date().toISOString(),\n          updated_at: new Date().toISOString()\n        }\n      ])\n      .select()\n      .single()\n\n    if (insertError) {\n      console.error('Error creating user profile:', insertError)\n      throw new Error('Failed to create user profile')\n    }\n\n    return newProfile\n  } catch (error) {\n    console.error('Error in ensureUserProfile:', error)\n    throw error\n  }\n}\n\nexport async function getUserProfile(userId: string): Promise<UserProfile | null> {\n  const supabase = await createClient()\n\n  try {\n    const { data: profile, error } = await supabase\n      .from('profiles')\n      .select('*')\n      .eq('id', userId)\n      .single()\n\n    if (error) {\n      console.error('Error fetching user profile:', error)\n      return null\n    }\n\n    return profile\n  } catch (error) {\n    console.error('Error in getUserProfile:', error)\n    return null\n  }\n}\n\nexport async function updateUserProfile(userId: string, updates: Partial<UserProfile>): Promise<UserProfile | null> {\n  const supabase = await createClient()\n\n  try {\n    const { data: updatedProfile, error } = await supabase\n      .from('profiles')\n      .update({\n        ...updates,\n        updated_at: new Date().toISOString()\n      })\n      .eq('id', userId)\n      .select()\n      .single()\n\n    if (error) {\n      console.error('Error updating user profile:', error)\n      throw new Error('Failed to update user profile')\n    }\n\n    return updatedProfile\n  } catch (error) {\n    console.error('Error in updateUserProfile:', error)\n    throw error\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AAWO,eAAe,kBAAkB,MAAc,EAAE,KAAa,EAAE,QAAiB;IACtF,MAAM,WAAW,MAAM,CAAA,GAAA,kIAAA,CAAA,eAAY,AAAD;IAElC,IAAI;QACF,yCAAyC;QACzC,MAAM,EAAE,MAAM,eAAe,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,SACxD,IAAI,CAAC,YACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,QACT,MAAM;QAET,IAAI,mBAAmB,CAAC,YAAY;YAClC,OAAO;QACT;QAEA,sCAAsC;QACtC,MAAM,EAAE,MAAM,UAAU,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SACpD,IAAI,CAAC,YACL,MAAM,CAAC;YACN;gBACE,IAAI;gBACJ,OAAO;gBACP,WAAW,YAAY,MAAM,KAAK,CAAC,IAAI,CAAC,EAAE;gBAC1C,YAAY,IAAI,OAAO,WAAW;gBAClC,YAAY,IAAI,OAAO,WAAW;YACpC;SACD,EACA,MAAM,GACN,MAAM;QAET,IAAI,aAAa;YACf,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,MAAM;IACR;AACF;AAEO,eAAe,eAAe,MAAc;IACjD,MAAM,WAAW,MAAM,CAAA,GAAA,kIAAA,CAAA,eAAY,AAAD;IAElC,IAAI;QACF,MAAM,EAAE,MAAM,OAAO,EAAE,KAAK,EAAE,GAAG,MAAM,SACpC,IAAI,CAAC,YACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,QACT,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,OAAO;QACT;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO;IACT;AACF;AAEO,eAAe,kBAAkB,MAAc,EAAE,OAA6B;IACnF,MAAM,WAAW,MAAM,CAAA,GAAA,kIAAA,CAAA,eAAY,AAAD;IAElC,IAAI;QACF,MAAM,EAAE,MAAM,cAAc,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3C,IAAI,CAAC,YACL,MAAM,CAAC;YACN,GAAG,OAAO;YACV,YAAY,IAAI,OAAO,WAAW;QACpC,GACC,EAAE,CAAC,MAAM,QACT,MAAM,GACN,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,MAAM;IACR;AACF", "debugId": null}}, {"offset": {"line": 1054, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/app/api/trips/route.ts"], "sourcesContent": ["import { createClient } from '@/lib/supabase/server'\nimport { NextRequest, NextResponse } from 'next/server'\nimport { withSecurity } from '@/lib/security/middleware'\nimport { addSecurityHeaders } from '@/lib/security/utils'\nimport { rateLimitConfigs } from '@/lib/security/rate-limit'\nimport { handleSecureError } from '@/lib/security/error-handler'\nimport { ensureUserProfile } from '@/lib/auth/profile-manager'\n\n// GET /api/trips - Get user's trips\nexport const GET = withSecurity(\n  async (request: NextRequest, context: any) => {\n    try {\n      const supabase = await createClient()\n      const user = context.user\n\n      // Ensure user profile exists\n      await ensureUserProfile(user.id, user.email, user.user_metadata?.full_name)\n\n      const { data: trips, error } = await supabase\n        .from('trips')\n        .select(`\n          id,\n          title,\n          description,\n          destination_country,\n          destination_city,\n          start_date,\n          end_date,\n          trip_type,\n          status,\n          budget_total,\n          budget_spent,\n          currency,\n          traveler_count,\n          is_shared,\n          cover_image_url,\n          notes,\n          created_at,\n          updated_at\n        `)\n        .order('created_at', { ascending: false })\n\n      if (error) throw error\n\n      const response = NextResponse.json({ trips: trips || [] })\n      return addSecurityHeaders(response)\n    } catch (error) {\n      return handleSecureError(error)\n    }\n  },\n  {\n    requireAuth: true,\n    rateLimit: rateLimitConfigs.api,\n    auditLog: true\n  }\n)\n\n// POST /api/trips - Create new trip\nexport const POST = withSecurity(\n  async (request: NextRequest, context: any) => {\n    try {\n      const requestData = await request.json()\n      const {\n        title,\n        description,\n        destination_country,\n        destination_city,\n        start_date,\n        end_date,\n        trip_type,\n        budget_total,\n        currency,\n        traveler_count,\n        cover_image_url,\n        notes\n      } = requestData\n      \n      // Basic validation\n      if (!title || typeof title !== 'string' || title.length > 200) {\n        return NextResponse.json({ \n          error: { code: 'VALIDATION_ERROR', message: 'Invalid title' } \n        }, { status: 400 })\n      }\n\n      if (!destination_country || typeof destination_country !== 'string') {\n        return NextResponse.json({ \n          error: { code: 'VALIDATION_ERROR', message: 'Destination country is required' } \n        }, { status: 400 })\n      }\n\n      if (!start_date || !end_date) {\n        return NextResponse.json({ \n          error: { code: 'VALIDATION_ERROR', message: 'Start and end dates are required' } \n        }, { status: 400 })\n      }\n\n      const supabase = await createClient()\n      const user = context.user\n\n      // Ensure user profile exists\n      await ensureUserProfile(user.id, user.email, user.user_metadata?.full_name)\n\n      const { data: trip, error } = await supabase\n        .from('trips')\n        .insert([\n          {\n            user_id: user.id,\n            title: title.trim(),\n            description: description || null,\n            destination_country: destination_country.trim(),\n            destination_city: destination_city?.trim() || null,\n            start_date,\n            end_date,\n            trip_type: trip_type || 'leisure',\n            budget_total: budget_total || null,\n            currency: currency || 'USD',\n            traveler_count: traveler_count || 1,\n            cover_image_url: cover_image_url || null,\n            notes: notes || null\n          },\n        ])\n        .select()\n        .single()\n\n      if (error) throw error\n\n      const response = NextResponse.json({ trip })\n      return addSecurityHeaders(response)\n    } catch (error) {\n      return handleSecureError(error)\n    }\n  },\n  {\n    requireAuth: true,\n    rateLimit: rateLimitConfigs.api,\n    auditLog: true\n  }\n)\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAGO,MAAM,MAAM,CAAA,GAAA,sIAAA,CAAA,eAAY,AAAD,EAC5B,OAAO,SAAsB;IAC3B,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,kIAAA,CAAA,eAAY,AAAD;QAClC,MAAM,OAAO,QAAQ,IAAI;QAEzB,6BAA6B;QAC7B,MAAM,CAAA,GAAA,0IAAA,CAAA,oBAAiB,AAAD,EAAE,KAAK,EAAE,EAAE,KAAK,KAAK,EAAE,KAAK,aAAa,EAAE;QAEjE,MAAM,EAAE,MAAM,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,SACL,MAAM,CAAC,CAAC;;;;;;;;;;;;;;;;;;;QAmBT,CAAC,EACA,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,OAAO,MAAM;QAEjB,MAAM,WAAW,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO,SAAS,EAAE;QAAC;QACxD,OAAO,CAAA,GAAA,iIAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B,EAAE,OAAO,OAAO;QACd,OAAO,CAAA,GAAA,4IAAA,CAAA,oBAAiB,AAAD,EAAE;IAC3B;AACF,GACA;IACE,aAAa;IACb,WAAW,yIAAA,CAAA,mBAAgB,CAAC,GAAG;IAC/B,UAAU;AACZ;AAIK,MAAM,OAAO,CAAA,GAAA,sIAAA,CAAA,eAAY,AAAD,EAC7B,OAAO,SAAsB;IAC3B,IAAI;QACF,MAAM,cAAc,MAAM,QAAQ,IAAI;QACtC,MAAM,EACJ,KAAK,EACL,WAAW,EACX,mBAAmB,EACnB,gBAAgB,EAChB,UAAU,EACV,QAAQ,EACR,SAAS,EACT,YAAY,EACZ,QAAQ,EACR,cAAc,EACd,eAAe,EACf,KAAK,EACN,GAAG;QAEJ,mBAAmB;QACnB,IAAI,CAAC,SAAS,OAAO,UAAU,YAAY,MAAM,MAAM,GAAG,KAAK;YAC7D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,OAAO;oBAAE,MAAM;oBAAoB,SAAS;gBAAgB;YAC9D,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,IAAI,CAAC,uBAAuB,OAAO,wBAAwB,UAAU;YACnE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,OAAO;oBAAE,MAAM;oBAAoB,SAAS;gBAAkC;YAChF,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,IAAI,CAAC,cAAc,CAAC,UAAU;YAC5B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,OAAO;oBAAE,MAAM;oBAAoB,SAAS;gBAAmC;YACjF,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,MAAM,WAAW,MAAM,CAAA,GAAA,kIAAA,CAAA,eAAY,AAAD;QAClC,MAAM,OAAO,QAAQ,IAAI;QAEzB,6BAA6B;QAC7B,MAAM,CAAA,GAAA,0IAAA,CAAA,oBAAiB,AAAD,EAAE,KAAK,EAAE,EAAE,KAAK,KAAK,EAAE,KAAK,aAAa,EAAE;QAEjE,MAAM,EAAE,MAAM,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SACjC,IAAI,CAAC,SACL,MAAM,CAAC;YACN;gBACE,SAAS,KAAK,EAAE;gBAChB,OAAO,MAAM,IAAI;gBACjB,aAAa,eAAe;gBAC5B,qBAAqB,oBAAoB,IAAI;gBAC7C,kBAAkB,kBAAkB,UAAU;gBAC9C;gBACA;gBACA,WAAW,aAAa;gBACxB,cAAc,gBAAgB;gBAC9B,UAAU,YAAY;gBACtB,gBAAgB,kBAAkB;gBAClC,iBAAiB,mBAAmB;gBACpC,OAAO,SAAS;YAClB;SACD,EACA,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QAEjB,MAAM,WAAW,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE;QAAK;QAC1C,OAAO,CAAA,GAAA,iIAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B,EAAE,OAAO,OAAO;QACd,OAAO,CAAA,GAAA,4IAAA,CAAA,oBAAiB,AAAD,EAAE;IAC3B;AACF,GACA;IACE,aAAa;IACb,WAAW,yIAAA,CAAA,mBAAgB,CAAC,GAAG;IAC/B,UAAU;AACZ", "debugId": null}}]}