{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/lib/services/trip-planner-service.ts"], "sourcesContent": ["export interface Trip {\n  id: string\n  user_id: string\n  title: string\n  description?: string\n  destination_country: string\n  destination_city?: string\n  start_date: string\n  end_date: string\n  trip_type: 'leisure' | 'business' | 'adventure' | 'family' | 'romantic' | 'solo' | 'group'\n  status: 'planning' | 'booked' | 'in_progress' | 'completed' | 'cancelled'\n  budget_total?: number\n  budget_spent: number\n  currency: string\n  traveler_count: number\n  is_shared: boolean\n  cover_image_url?: string\n  notes?: string\n  created_at: string\n  updated_at: string\n  trip_destinations?: TripDestination[]\n}\n\nexport interface TripDestination {\n  id: string\n  trip_id: string\n  name: string\n  country: string\n  city?: string\n  latitude?: number\n  longitude?: number\n  arrival_date?: string\n  departure_date?: string\n  order_index: number\n  accommodation_name?: string\n  accommodation_address?: string\n  accommodation_cost?: number\n  notes?: string\n  created_at: string\n  updated_at: string\n  trip_activities?: TripActivity[]\n}\n\nexport interface TripActivity {\n  id: string\n  trip_id: string\n  destination_id?: string\n  title: string\n  description?: string\n  category: 'sightseeing' | 'dining' | 'entertainment' | 'shopping' | 'transport' | 'accommodation' | 'adventure' | 'cultural' | 'relaxation' | 'business'\n  scheduled_date?: string\n  scheduled_time?: string\n  duration_minutes?: number\n  cost?: number\n  currency: string\n  location_name?: string\n  location_address?: string\n  latitude?: number\n  longitude?: number\n  booking_reference?: string\n  booking_url?: string\n  status: 'planned' | 'booked' | 'completed' | 'cancelled'\n  priority: 'low' | 'medium' | 'high' | 'must_do'\n  notes?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface TripExpense {\n  id: string\n  trip_id: string\n  destination_id?: string\n  activity_id?: string\n  category: 'accommodation' | 'transport' | 'food' | 'activities' | 'shopping' | 'fuel' | 'parking' | 'tolls' | 'insurance' | 'visas' | 'other'\n  subcategory?: string\n  description: string\n  amount: number\n  currency: string\n  exchange_rate: number\n  amount_usd: number\n  expense_date: string\n  payment_method?: string\n  receipt_url?: string\n  is_shared_expense: boolean\n  shared_with_count: number\n  notes?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface TripTransport {\n  id: string\n  trip_id: string\n  from_destination_id?: string\n  to_destination_id?: string\n  transport_type: 'flight' | 'train' | 'bus' | 'car_rental' | 'taxi' | 'rideshare' | 'ferry' | 'walking' | 'cycling' | 'other'\n  provider_name?: string\n  departure_datetime?: string\n  arrival_datetime?: string\n  duration_minutes?: number\n  distance_km?: number\n  cost?: number\n  currency: string\n  booking_reference?: string\n  booking_url?: string\n  from_location?: string\n  to_location?: string\n  from_latitude?: number\n  from_longitude?: number\n  to_latitude?: number\n  to_longitude?: number\n  fuel_cost?: number\n  fuel_consumption_liters?: number\n  toll_costs?: number\n  parking_costs?: number\n  notes?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface CreateTripData {\n  title: string\n  description?: string\n  destination_country: string\n  destination_city?: string\n  start_date: string\n  end_date: string\n  trip_type?: 'leisure' | 'business' | 'adventure' | 'family' | 'romantic' | 'solo' | 'group'\n  budget_total?: number\n  currency?: string\n  traveler_count?: number\n  cover_image_url?: string\n  notes?: string\n}\n\nexport interface UpdateTripData extends Partial<CreateTripData> {}\n\nexport interface CreateDestinationData {\n  name: string\n  country: string\n  city?: string\n  latitude?: number\n  longitude?: number\n  arrival_date?: string\n  departure_date?: string\n  order_index?: number\n  accommodation_name?: string\n  accommodation_address?: string\n  accommodation_cost?: number\n  notes?: string\n}\n\nexport interface CreateActivityData {\n  destination_id?: string\n  title: string\n  description?: string\n  category: 'sightseeing' | 'dining' | 'entertainment' | 'shopping' | 'transport' | 'accommodation' | 'adventure' | 'cultural' | 'relaxation' | 'business'\n  scheduled_date?: string\n  scheduled_time?: string\n  duration_minutes?: number\n  cost?: number\n  currency?: string\n  location_name?: string\n  location_address?: string\n  latitude?: number\n  longitude?: number\n  booking_reference?: string\n  booking_url?: string\n  priority?: 'low' | 'medium' | 'high' | 'must_do'\n  notes?: string\n}\n\nexport interface CreateExpenseData {\n  destination_id?: string\n  activity_id?: string\n  category: 'accommodation' | 'transport' | 'food' | 'activities' | 'shopping' | 'fuel' | 'parking' | 'tolls' | 'insurance' | 'visas' | 'other'\n  subcategory?: string\n  description: string\n  amount: number\n  currency?: string\n  exchange_rate?: number\n  expense_date: string\n  payment_method?: string\n  receipt_url?: string\n  is_shared_expense?: boolean\n  shared_with_count?: number\n  notes?: string\n}\n\nclass TripPlannerService {\n  private baseUrl = '/api/trips'\n\n  // Trip CRUD operations\n  async getTrips(): Promise<Trip[]> {\n    const response = await fetch(this.baseUrl, {\n      method: 'GET',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    })\n\n    if (!response.ok) {\n      throw new Error(`Failed to fetch trips: ${response.statusText}`)\n    }\n\n    const data = await response.json()\n    return data.trips || []\n  }\n\n  async getTrip(id: string): Promise<Trip> {\n    const response = await fetch(`${this.baseUrl}/${id}`, {\n      method: 'GET',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    })\n\n    if (!response.ok) {\n      throw new Error(`Failed to fetch trip: ${response.statusText}`)\n    }\n\n    const data = await response.json()\n    return data.trip\n  }\n\n  async createTrip(data: CreateTripData): Promise<Trip> {\n    const response = await fetch(this.baseUrl, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(data),\n    })\n\n    if (!response.ok) {\n      throw new Error(`Failed to create trip: ${response.statusText}`)\n    }\n\n    const result = await response.json()\n    return result.trip\n  }\n\n  async updateTrip(id: string, data: UpdateTripData): Promise<Trip> {\n    const response = await fetch(`${this.baseUrl}/${id}`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(data),\n    })\n\n    if (!response.ok) {\n      throw new Error(`Failed to update trip: ${response.statusText}`)\n    }\n\n    const result = await response.json()\n    return result.trip\n  }\n\n  async deleteTrip(id: string): Promise<void> {\n    const response = await fetch(`${this.baseUrl}/${id}`, {\n      method: 'DELETE',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    })\n\n    if (!response.ok) {\n      throw new Error(`Failed to delete trip: ${response.statusText}`)\n    }\n  }\n\n  // Destination operations\n  async getDestinations(tripId: string): Promise<TripDestination[]> {\n    const response = await fetch(`${this.baseUrl}/${tripId}/destinations`, {\n      method: 'GET',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    })\n\n    if (!response.ok) {\n      throw new Error(`Failed to fetch destinations: ${response.statusText}`)\n    }\n\n    const data = await response.json()\n    return data.destinations || []\n  }\n\n  async addDestination(tripId: string, data: CreateDestinationData): Promise<TripDestination> {\n    const response = await fetch(`${this.baseUrl}/${tripId}/destinations`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(data),\n    })\n\n    if (!response.ok) {\n      throw new Error(`Failed to add destination: ${response.statusText}`)\n    }\n\n    const result = await response.json()\n    return result.destination\n  }\n\n  // Activity operations\n  async getActivities(tripId: string): Promise<TripActivity[]> {\n    const response = await fetch(`${this.baseUrl}/${tripId}/activities`, {\n      method: 'GET',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    })\n\n    if (!response.ok) {\n      throw new Error(`Failed to fetch activities: ${response.statusText}`)\n    }\n\n    const data = await response.json()\n    return data.activities || []\n  }\n\n  async addActivity(tripId: string, data: CreateActivityData): Promise<TripActivity> {\n    const response = await fetch(`${this.baseUrl}/${tripId}/activities`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(data),\n    })\n\n    if (!response.ok) {\n      throw new Error(`Failed to add activity: ${response.statusText}`)\n    }\n\n    const result = await response.json()\n    return result.activity\n  }\n\n  // Expense operations\n  async getExpenses(tripId: string): Promise<TripExpense[]> {\n    const response = await fetch(`${this.baseUrl}/${tripId}/expenses`, {\n      method: 'GET',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    })\n\n    if (!response.ok) {\n      throw new Error(`Failed to fetch expenses: ${response.statusText}`)\n    }\n\n    const data = await response.json()\n    return data.expenses || []\n  }\n\n  async addExpense(tripId: string, data: CreateExpenseData): Promise<TripExpense> {\n    const response = await fetch(`${this.baseUrl}/${tripId}/expenses`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(data),\n    })\n\n    if (!response.ok) {\n      throw new Error(`Failed to add expense: ${response.statusText}`)\n    }\n\n    const result = await response.json()\n    return result.expense\n  }\n\n  // Utility methods\n  calculateTripDuration(startDate: string, endDate: string): number {\n    const start = new Date(startDate)\n    const end = new Date(endDate)\n    const diffTime = Math.abs(end.getTime() - start.getTime())\n    return Math.ceil(diffTime / (1000 * 60 * 60 * 24))\n  }\n\n  calculateBudgetProgress(budgetTotal?: number, budgetSpent?: number): number {\n    if (!budgetTotal || budgetTotal === 0) return 0\n    return Math.min((budgetSpent || 0) / budgetTotal * 100, 100)\n  }\n\n  formatCurrency(amount: number, currency: string = 'USD'): string {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: currency,\n    }).format(amount)\n  }\n}\n\nexport const tripPlannerService = new TripPlannerService()\n"], "names": [], "mappings": ";;;AA6LA,MAAM;IACI,UAAU,aAAY;IAE9B,uBAAuB;IACvB,MAAM,WAA4B;QAChC,MAAM,WAAW,MAAM,MAAM,IAAI,CAAC,OAAO,EAAE;YACzC,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,uBAAuB,EAAE,SAAS,UAAU,EAAE;QACjE;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO,KAAK,KAAK,IAAI,EAAE;IACzB;IAEA,MAAM,QAAQ,EAAU,EAAiB;QACvC,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE;YACpD,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,sBAAsB,EAAE,SAAS,UAAU,EAAE;QAChE;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO,KAAK,IAAI;IAClB;IAEA,MAAM,WAAW,IAAoB,EAAiB;QACpD,MAAM,WAAW,MAAM,MAAM,IAAI,CAAC,OAAO,EAAE;YACzC,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,uBAAuB,EAAE,SAAS,UAAU,EAAE;QACjE;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAClC,OAAO,OAAO,IAAI;IACpB;IAEA,MAAM,WAAW,EAAU,EAAE,IAAoB,EAAiB;QAChE,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE;YACpD,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,uBAAuB,EAAE,SAAS,UAAU,EAAE;QACjE;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAClC,OAAO,OAAO,IAAI;IACpB;IAEA,MAAM,WAAW,EAAU,EAAiB;QAC1C,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE;YACpD,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,uBAAuB,EAAE,SAAS,UAAU,EAAE;QACjE;IACF;IAEA,yBAAyB;IACzB,MAAM,gBAAgB,MAAc,EAA8B;QAChE,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,OAAO,aAAa,CAAC,EAAE;YACrE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,8BAA8B,EAAE,SAAS,UAAU,EAAE;QACxE;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO,KAAK,YAAY,IAAI,EAAE;IAChC;IAEA,MAAM,eAAe,MAAc,EAAE,IAA2B,EAA4B;QAC1F,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,OAAO,aAAa,CAAC,EAAE;YACrE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,2BAA2B,EAAE,SAAS,UAAU,EAAE;QACrE;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAClC,OAAO,OAAO,WAAW;IAC3B;IAEA,sBAAsB;IACtB,MAAM,cAAc,MAAc,EAA2B;QAC3D,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,OAAO,WAAW,CAAC,EAAE;YACnE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,4BAA4B,EAAE,SAAS,UAAU,EAAE;QACtE;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO,KAAK,UAAU,IAAI,EAAE;IAC9B;IAEA,MAAM,YAAY,MAAc,EAAE,IAAwB,EAAyB;QACjF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,OAAO,WAAW,CAAC,EAAE;YACnE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,SAAS,UAAU,EAAE;QAClE;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAClC,OAAO,OAAO,QAAQ;IACxB;IAEA,qBAAqB;IACrB,MAAM,YAAY,MAAc,EAA0B;QACxD,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,OAAO,SAAS,CAAC,EAAE;YACjE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,0BAA0B,EAAE,SAAS,UAAU,EAAE;QACpE;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO,KAAK,QAAQ,IAAI,EAAE;IAC5B;IAEA,MAAM,WAAW,MAAc,EAAE,IAAuB,EAAwB;QAC9E,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,OAAO,SAAS,CAAC,EAAE;YACjE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,uBAAuB,EAAE,SAAS,UAAU,EAAE;QACjE;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAClC,OAAO,OAAO,OAAO;IACvB;IAEA,kBAAkB;IAClB,sBAAsB,SAAiB,EAAE,OAAe,EAAU;QAChE,MAAM,QAAQ,IAAI,KAAK;QACvB,MAAM,MAAM,IAAI,KAAK;QACrB,MAAM,WAAW,KAAK,GAAG,CAAC,IAAI,OAAO,KAAK,MAAM,OAAO;QACvD,OAAO,KAAK,IAAI,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;IAClD;IAEA,wBAAwB,WAAoB,EAAE,WAAoB,EAAU;QAC1E,IAAI,CAAC,eAAe,gBAAgB,GAAG,OAAO;QAC9C,OAAO,KAAK,GAAG,CAAC,CAAC,eAAe,CAAC,IAAI,cAAc,KAAK;IAC1D;IAEA,eAAe,MAAc,EAAE,WAAmB,KAAK,EAAU;QAC/D,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;QACZ,GAAG,MAAM,CAAC;IACZ;AACF;AAEO,MAAM,qBAAqB,IAAI", "debugId": null}}, {"offset": {"line": 190, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 209, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/components/ui/Button.tsx"], "sourcesContent": ["import { ButtonHTMLAttributes, forwardRef } from 'react'\nimport { cn } from '@/lib/utils'\nimport { cva, type VariantProps } from 'class-variance-authority'\n\nconst buttonVariants = cva(\n  'inline-flex items-center justify-center rounded-lg text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none disabled:cursor-not-allowed ring-offset-background transform hover:scale-105 active:scale-95 focus:scale-105',\n  {\n    variants: {\n      variant: {\n        default: 'bg-gradient-to-r from-blue-600 to-blue-700 text-white hover:from-blue-700 hover:to-blue-800 shadow-lg hover:shadow-xl focus-visible:ring-blue-500',\n        destructive: 'bg-gradient-to-r from-red-600 to-red-700 text-white hover:from-red-700 hover:to-red-800 shadow-lg hover:shadow-xl focus-visible:ring-red-500',\n        outline: 'border-2 border-gray-300 bg-white text-gray-700 hover:bg-gray-50 hover:border-gray-400 focus-visible:ring-gray-500',\n        secondary: 'bg-gradient-to-r from-gray-100 to-gray-200 text-gray-900 hover:from-gray-200 hover:to-gray-300 focus-visible:ring-gray-500',\n        ghost: 'hover:bg-gray-100 text-gray-700 focus-visible:ring-gray-500',\n        link: 'underline-offset-4 hover:underline text-blue-600 focus-visible:ring-blue-500',\n        success: 'bg-gradient-to-r from-green-600 to-green-700 text-white hover:from-green-700 hover:to-green-800 shadow-lg hover:shadow-xl focus-visible:ring-green-500',\n        warning: 'bg-gradient-to-r from-yellow-500 to-yellow-600 text-white hover:from-yellow-600 hover:to-yellow-700 shadow-lg hover:shadow-xl focus-visible:ring-yellow-500',\n      },\n      size: {\n        default: 'h-10 py-2 px-4',\n        sm: 'h-9 px-3 text-xs',\n        lg: 'h-12 px-8 text-base',\n        xl: 'h-14 px-10 text-lg',\n        icon: 'h-10 w-10',\n      },\n    },\n    defaultVariants: {\n      variant: 'default',\n      size: 'default',\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  loading?: boolean\n  loadingText?: string\n}\n\nconst Button = forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, loading, loadingText, children, disabled, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        disabled={disabled || loading}\n        aria-disabled={disabled || loading}\n        {...props}\n      >\n        {loading ? (\n          <>\n            <svg\n              className=\"animate-spin -ml-1 mr-2 h-4 w-4\"\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              aria-hidden=\"true\"\n            >\n              <circle\n                className=\"opacity-25\"\n                cx=\"12\"\n                cy=\"12\"\n                r=\"10\"\n                stroke=\"currentColor\"\n                strokeWidth=\"4\"\n              ></circle>\n              <path\n                className=\"opacity-75\"\n                fill=\"currentColor\"\n                d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n              ></path>\n            </svg>\n            {loadingText || 'Loading...'}\n          </>\n        ) : (\n          children\n        )}\n      </button>\n    )\n  }\n)\nButton.displayName = 'Button'\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,gVACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aAAa;YACb,SAAS;YACT,WAAW;YACX,OAAO;YACP,MAAM;YACN,SAAS;YACT,SAAS;QACX;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAUF,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,OACtB,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACjF,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACL,UAAU,YAAY;QACtB,iBAAe,YAAY;QAC1B,GAAG,KAAK;kBAER,wBACC;;8BACE,6LAAC;oBACC,WAAU;oBACV,OAAM;oBACN,MAAK;oBACL,SAAQ;oBACR,eAAY;;sCAEZ,6LAAC;4BACC,WAAU;4BACV,IAAG;4BACH,IAAG;4BACH,GAAE;4BACF,QAAO;4BACP,aAAY;;;;;;sCAEd,6LAAC;4BACC,WAAU;4BACV,MAAK;4BACL,GAAE;;;;;;;;;;;;gBAGL,eAAe;;2BAGlB;;;;;;AAIR;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 317, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/components/ui/Input.tsx"], "sourcesContent": ["import { InputHTMLAttributes, forwardRef } from 'react'\nimport { cn } from '@/lib/utils'\n\nexport interface InputProps extends InputHTMLAttributes<HTMLInputElement> {\n  error?: boolean\n  helperText?: string\n  label?: string\n}\n\nconst Input = forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, error, helperText, label, id, ...props }, ref) => {\n    const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`\n    const helperTextId = helperText ? `${inputId}-helper` : undefined\n\n    return (\n      <div className=\"w-full\">\n        {label && (\n          <label\n            htmlFor={inputId}\n            className=\"block text-sm font-medium text-gray-700 mb-1\"\n          >\n            {label}\n            {props.required && <span className=\"text-red-500 ml-1\" aria-label=\"required\">*</span>}\n          </label>\n        )}\n        <input\n          id={inputId}\n          type={type}\n          className={cn(\n            'flex h-10 w-full rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm ring-offset-white file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 transition-all duration-200 hover:border-gray-400',\n            error && 'border-red-500 focus-visible:ring-red-500 aria-invalid:border-red-500',\n            className\n          )}\n          ref={ref}\n          aria-invalid={error}\n          aria-describedby={helperTextId}\n          {...props}\n        />\n        {helperText && (\n          <p\n            id={helperTextId}\n            className={cn(\n              'mt-1 text-xs',\n              error ? 'text-red-600' : 'text-gray-500'\n            )}\n            role={error ? 'alert' : 'status'}\n          >\n            {helperText}\n          </p>\n        )}\n      </div>\n    )\n  }\n)\nInput.displayName = 'Input'\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAQA,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,OACrB,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE,EAAE,EAAE,GAAG,OAAO,EAAE;IAC5D,MAAM,UAAU,MAAM,CAAC,MAAM,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;IACxE,MAAM,eAAe,aAAa,GAAG,QAAQ,OAAO,CAAC,GAAG;IAExD,qBACE,6LAAC;QAAI,WAAU;;YACZ,uBACC,6LAAC;gBACC,SAAS;gBACT,WAAU;;oBAET;oBACA,MAAM,QAAQ,kBAAI,6LAAC;wBAAK,WAAU;wBAAoB,cAAW;kCAAW;;;;;;;;;;;;0BAGjF,6LAAC;gBACC,IAAI;gBACJ,MAAM;gBACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uYACA,SAAS,yEACT;gBAEF,KAAK;gBACL,gBAAc;gBACd,oBAAkB;gBACjB,GAAG,KAAK;;;;;;YAEV,4BACC,6LAAC;gBACC,IAAI;gBACJ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gBACA,QAAQ,iBAAiB;gBAE3B,MAAM,QAAQ,UAAU;0BAEvB;;;;;;;;;;;;AAKX;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 397, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/components/ui/Loading.tsx"], "sourcesContent": ["import { cn } from '@/lib/utils'\n\ninterface LoadingProps {\n  size?: 'sm' | 'md' | 'lg' | 'xl'\n  variant?: 'spinner' | 'dots' | 'pulse'\n  className?: string\n  text?: string\n}\n\nconst sizeClasses = {\n  sm: 'h-4 w-4',\n  md: 'h-6 w-6',\n  lg: 'h-8 w-8',\n  xl: 'h-12 w-12',\n}\n\nexport function Loading({ \n  size = 'md', \n  variant = 'spinner', \n  className,\n  text \n}: LoadingProps) {\n  if (variant === 'spinner') {\n    return (\n      <div className={cn('flex flex-col items-center justify-center space-y-2', className)}>\n        <div\n          className={cn(\n            'animate-spin rounded-full border-2 border-gray-300 border-t-blue-600',\n            sizeClasses[size]\n          )}\n        />\n        {text && <p className=\"text-sm text-gray-600\">{text}</p>}\n      </div>\n    )\n  }\n\n  if (variant === 'dots') {\n    return (\n      <div className={cn('flex flex-col items-center justify-center space-y-2', className)}>\n        <div className=\"flex space-x-1\">\n          <div className=\"h-2 w-2 bg-blue-600 rounded-full animate-bounce [animation-delay:-0.3s]\"></div>\n          <div className=\"h-2 w-2 bg-blue-600 rounded-full animate-bounce [animation-delay:-0.15s]\"></div>\n          <div className=\"h-2 w-2 bg-blue-600 rounded-full animate-bounce\"></div>\n        </div>\n        {text && <p className=\"text-sm text-gray-600\">{text}</p>}\n      </div>\n    )\n  }\n\n  if (variant === 'pulse') {\n    return (\n      <div className={cn('flex flex-col items-center justify-center space-y-2', className)}>\n        <div\n          className={cn(\n            'bg-blue-600 rounded-full animate-pulse',\n            sizeClasses[size]\n          )}\n        />\n        {text && <p className=\"text-sm text-gray-600\">{text}</p>}\n      </div>\n    )\n  }\n\n  return null\n}\n\nexport function LoadingSkeleton({ className }: { className?: string }) {\n  return (\n    <div className={cn('animate-pulse', className)}>\n      <div className=\"bg-gray-200 rounded-lg h-4 w-full\"></div>\n    </div>\n  )\n}\n\nexport function LoadingCard() {\n  return (\n    <div className=\"bg-white rounded-xl border border-gray-200 shadow-sm p-6 animate-pulse\">\n      <div className=\"space-y-4\">\n        <div className=\"h-4 bg-gray-200 rounded w-3/4\"></div>\n        <div className=\"space-y-2\">\n          <div className=\"h-3 bg-gray-200 rounded\"></div>\n          <div className=\"h-3 bg-gray-200 rounded w-5/6\"></div>\n        </div>\n        <div className=\"h-8 bg-gray-200 rounded w-1/4\"></div>\n      </div>\n    </div>\n  )\n}\n\nexport function LoadingTable({ rows = 5 }: { rows?: number }) {\n  return (\n    <div className=\"bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden\">\n      <div className=\"p-6 border-b border-gray-200\">\n        <div className=\"h-6 bg-gray-200 rounded w-1/3 animate-pulse\"></div>\n      </div>\n      <div className=\"divide-y divide-gray-200\">\n        {Array.from({ length: rows }).map((_, i) => (\n          <div key={i} className=\"p-4 animate-pulse\">\n            <div className=\"flex items-center space-x-4\">\n              <div className=\"h-4 w-4 bg-gray-200 rounded\"></div>\n              <div className=\"flex-1 space-y-2\">\n                <div className=\"h-4 bg-gray-200 rounded w-3/4\"></div>\n                <div className=\"h-3 bg-gray-200 rounded w-1/2\"></div>\n              </div>\n              <div className=\"h-8 w-8 bg-gray-200 rounded\"></div>\n            </div>\n          </div>\n        ))}\n      </div>\n    </div>\n  )\n}\n\nexport function LoadingStats() {\n  return (\n    <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n      {Array.from({ length: 4 }).map((_, i) => (\n        <div key={i} className=\"bg-white rounded-xl border border-gray-200 shadow-sm p-4 animate-pulse\">\n          <div className=\"text-center space-y-2\">\n            <div className=\"h-8 bg-gray-200 rounded w-16 mx-auto\"></div>\n            <div className=\"h-4 bg-gray-200 rounded w-20 mx-auto\"></div>\n          </div>\n        </div>\n      ))}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;;;AASA,MAAM,cAAc;IAClB,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;AACN;AAEO,SAAS,QAAQ,EACtB,OAAO,IAAI,EACX,UAAU,SAAS,EACnB,SAAS,EACT,IAAI,EACS;IACb,IAAI,YAAY,WAAW;QACzB,qBACE,6LAAC;YAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uDAAuD;;8BACxE,6LAAC;oBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wEACA,WAAW,CAAC,KAAK;;;;;;gBAGpB,sBAAQ,6LAAC;oBAAE,WAAU;8BAAyB;;;;;;;;;;;;IAGrD;IAEA,IAAI,YAAY,QAAQ;QACtB,qBACE,6LAAC;YAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uDAAuD;;8BACxE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;;;;;;;gBAEhB,sBAAQ,6LAAC;oBAAE,WAAU;8BAAyB;;;;;;;;;;;;IAGrD;IAEA,IAAI,YAAY,SAAS;QACvB,qBACE,6LAAC;YAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uDAAuD;;8BACxE,6LAAC;oBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0CACA,WAAW,CAAC,KAAK;;;;;;gBAGpB,sBAAQ,6LAAC;oBAAE,WAAU;8BAAyB;;;;;;;;;;;;IAGrD;IAEA,OAAO;AACT;KAhDgB;AAkDT,SAAS,gBAAgB,EAAE,SAAS,EAA0B;IACnE,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;kBAClC,cAAA,6LAAC;YAAI,WAAU;;;;;;;;;;;AAGrB;MANgB;AAQT,SAAS;IACd,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;;;;;8BACf,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;;;;;;;8BAEjB,6LAAC;oBAAI,WAAU;;;;;;;;;;;;;;;;;AAIvB;MAbgB;AAeT,SAAS,aAAa,EAAE,OAAO,CAAC,EAAqB;IAC1D,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;;;;;;;;;;0BAEjB,6LAAC;gBAAI,WAAU;0BACZ,MAAM,IAAI,CAAC;oBAAE,QAAQ;gBAAK,GAAG,GAAG,CAAC,CAAC,GAAG,kBACpC,6LAAC;wBAAY,WAAU;kCACrB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;;;;;;;8CAEjB,6LAAC;oCAAI,WAAU;;;;;;;;;;;;uBAPT;;;;;;;;;;;;;;;;AAcpB;MAtBgB;AAwBT,SAAS;IACd,qBACE,6LAAC;QAAI,WAAU;kBACZ,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBACjC,6LAAC;gBAAY,WAAU;0BACrB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;;;;;;;eAHT;;;;;;;;;;AASlB;MAbgB", "debugId": null}}, {"offset": {"line": 737, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/components/ui/Card.tsx"], "sourcesContent": ["import { HTMLAttributes, forwardRef } from 'react'\nimport { cn } from '@/lib/utils'\n\nconst Card = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(\n        'rounded-xl border border-gray-200 bg-white text-gray-950 shadow-sm hover:shadow-md transition-all duration-200 backdrop-blur-sm',\n        className\n      )}\n      {...props}\n    />\n  )\n)\nCard.displayName = 'Card'\n\nconst CardHeader = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div ref={ref} className={cn('flex flex-col space-y-1.5 p-6', className)} {...props} />\n  )\n)\nCardHeader.displayName = 'CardHeader'\n\nconst CardTitle = forwardRef<HTMLParagraphElement, HTMLAttributes<HTMLHeadingElement>>(\n  ({ className, ...props }, ref) => (\n    <h3\n      ref={ref}\n      className={cn('text-lg font-semibold leading-none tracking-tight text-gray-900', className)}\n      {...props}\n    />\n  )\n)\nCardTitle.displayName = 'CardTitle'\n\nconst CardDescription = forwardRef<HTMLParagraphElement, HTMLAttributes<HTMLParagraphElement>>(\n  ({ className, ...props }, ref) => (\n    <p ref={ref} className={cn('text-sm text-gray-600', className)} {...props} />\n  )\n)\nCardDescription.displayName = 'CardDescription'\n\nconst CardContent = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div ref={ref} className={cn('p-6 pt-0', className)} {...props} />\n  )\n)\nCardContent.displayName = 'CardContent'\n\nconst CardFooter = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div ref={ref} className={cn('flex items-center p-6 pt-0', className)} {...props} />\n  )\n)\nCardFooter.displayName = 'CardFooter'\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,OACpB,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;;AAIf,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,QAC1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAAa,GAAG,KAAK;;;;;;;AAGvF,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,QACzB,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mEAAmE;QAChF,GAAG,KAAK;;;;;;;AAIf,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,QAC/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,6LAAC;QAAE,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QAAa,GAAG,KAAK;;;;;;;AAG7E,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,QAC3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAGlE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,SAC1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAAa,GAAG,KAAK;;;;;;;AAGpF,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 840, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/components/ui/Badge.tsx"], "sourcesContent": ["import { HTMLAttributes, forwardRef } from 'react'\nimport { cva, type VariantProps } from 'class-variance-authority'\nimport { cn } from '@/lib/utils'\n\nconst badgeVariants = cva(\n  'inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium transition-all duration-200',\n  {\n    variants: {\n      variant: {\n        default: 'bg-blue-100 text-blue-800 hover:bg-blue-200',\n        secondary: 'bg-gray-100 text-gray-800 hover:bg-gray-200',\n        destructive: 'bg-red-100 text-red-800 hover:bg-red-200',\n        success: 'bg-green-100 text-green-800 hover:bg-green-200',\n        warning: 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200',\n        outline: 'border border-gray-300 text-gray-700 hover:bg-gray-50',\n        purple: 'bg-purple-100 text-purple-800 hover:bg-purple-200',\n        indigo: 'bg-indigo-100 text-indigo-800 hover:bg-indigo-200',\n        pink: 'bg-pink-100 text-pink-800 hover:bg-pink-200',\n        orange: 'bg-orange-100 text-orange-800 hover:bg-orange-200',\n      },\n      size: {\n        default: 'px-2.5 py-0.5 text-xs',\n        sm: 'px-2 py-0.5 text-xs',\n        lg: 'px-3 py-1 text-sm',\n      },\n    },\n    defaultVariants: {\n      variant: 'default',\n      size: 'default',\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nconst Badge = forwardRef<HTMLDivElement, BadgeProps>(\n  ({ className, variant, size, ...props }, ref) => {\n    return (\n      <div\n        ref={ref}\n        className={cn(badgeVariants({ variant, size }), className)}\n        {...props}\n      />\n    )\n  }\n)\nBadge.displayName = 'Badge'\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,uGACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,WAAW;YACX,aAAa;YACb,SAAS;YACT,SAAS;YACT,SAAS;YACT,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,QAAQ;QACV;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAOF,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,OACrB,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IACvC,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;YAAS;QAAK,IAAI;QAC/C,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 906, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/components/trip-planner/TripCard.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { motion } from 'framer-motion'\nimport { Trip } from '@/lib/services/trip-planner-service'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'\nimport { Badge } from '@/components/ui/Badge'\nimport { Button } from '@/components/ui/Button'\nimport { \n  MapPinIcon, \n  CalendarIcon, \n  DollarSignIcon, \n  UsersIcon, \n  EditIcon, \n  TrashIcon,\n  ShareIcon,\n  EyeIcon,\n  ClockIcon\n} from 'lucide-react'\nimport Link from 'next/link'\n\ninterface TripCardProps {\n  trip: Trip\n  onEdit?: (trip: Trip) => void\n  onDelete?: (tripId: string) => void\n  onShare?: (tripId: string) => void\n}\n\nexport default function TripCard({ trip, onEdit, onDelete, onShare }: TripCardProps) {\n  const [isHovered, setIsHovered] = useState(false)\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'planning': return 'bg-blue-100 text-blue-800'\n      case 'booked': return 'bg-green-100 text-green-800'\n      case 'in_progress': return 'bg-yellow-100 text-yellow-800'\n      case 'completed': return 'bg-gray-100 text-gray-800'\n      case 'cancelled': return 'bg-red-100 text-red-800'\n      default: return 'bg-gray-100 text-gray-800'\n    }\n  }\n\n  const getTripTypeColor = (type: string) => {\n    switch (type) {\n      case 'leisure': return 'bg-purple-100 text-purple-800'\n      case 'business': return 'bg-blue-100 text-blue-800'\n      case 'adventure': return 'bg-orange-100 text-orange-800'\n      case 'family': return 'bg-green-100 text-green-800'\n      case 'romantic': return 'bg-pink-100 text-pink-800'\n      case 'solo': return 'bg-indigo-100 text-indigo-800'\n      case 'group': return 'bg-yellow-100 text-yellow-800'\n      default: return 'bg-gray-100 text-gray-800'\n    }\n  }\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      month: 'short',\n      day: 'numeric',\n      year: 'numeric'\n    })\n  }\n\n  const calculateDuration = () => {\n    const start = new Date(trip.start_date)\n    const end = new Date(trip.end_date)\n    const diffTime = Math.abs(end.getTime() - start.getTime())\n    return Math.ceil(diffTime / (1000 * 60 * 60 * 24))\n  }\n\n  const budgetProgress = trip.budget_total \n    ? Math.min((trip.budget_spent / trip.budget_total) * 100, 100)\n    : 0\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      whileHover={{ y: -5, scale: 1.02 }}\n      onHoverStart={() => setIsHovered(true)}\n      onHoverEnd={() => setIsHovered(false)}\n      className=\"group\"\n    >\n      <Card className=\"h-full overflow-hidden border-2 border-transparent hover:border-blue-200 transition-all duration-300 shadow-sm hover:shadow-lg\">\n        {/* Cover Image */}\n        <div className=\"relative h-48 bg-gradient-to-br from-blue-500 to-purple-600 overflow-hidden\">\n          {trip.cover_image_url ? (\n            <img \n              src={trip.cover_image_url} \n              alt={trip.title}\n              className=\"w-full h-full object-cover\"\n            />\n          ) : (\n            <div className=\"w-full h-full flex items-center justify-center\">\n              <MapPinIcon className=\"h-16 w-16 text-white opacity-50\" />\n            </div>\n          )}\n          \n          {/* Status Badge */}\n          <div className=\"absolute top-3 left-3\">\n            <Badge className={`${getStatusColor(trip.status)} font-medium`}>\n              {trip.status.replace('_', ' ').toUpperCase()}\n            </Badge>\n          </div>\n\n          {/* Shared Badge */}\n          {trip.is_shared && (\n            <div className=\"absolute top-3 right-3\">\n              <Badge className=\"bg-white/20 text-white backdrop-blur-sm\">\n                <ShareIcon className=\"h-3 w-3 mr-1\" />\n                Shared\n              </Badge>\n            </div>\n          )}\n\n          {/* Action Buttons */}\n          <motion.div \n            className=\"absolute bottom-3 right-3 flex space-x-2\"\n            initial={{ opacity: 0 }}\n            animate={{ opacity: isHovered ? 1 : 0 }}\n            transition={{ duration: 0.2 }}\n          >\n            <Button size=\"sm\" variant=\"secondary\" className=\"bg-white/20 backdrop-blur-sm text-white hover:bg-white/30\">\n              <Link href={`/trips/${trip.id}`}>\n                <EyeIcon className=\"h-4 w-4\" />\n              </Link>\n            </Button>\n            {onEdit && (\n              <Button \n                size=\"sm\" \n                variant=\"secondary\" \n                className=\"bg-white/20 backdrop-blur-sm text-white hover:bg-white/30\"\n                onClick={() => onEdit(trip)}\n              >\n                <EditIcon className=\"h-4 w-4\" />\n              </Button>\n            )}\n            {onShare && (\n              <Button \n                size=\"sm\" \n                variant=\"secondary\" \n                className=\"bg-white/20 backdrop-blur-sm text-white hover:bg-white/30\"\n                onClick={() => onShare(trip.id)}\n              >\n                <ShareIcon className=\"h-4 w-4\" />\n              </Button>\n            )}\n          </motion.div>\n        </div>\n\n        <CardHeader className=\"pb-3\">\n          <div className=\"flex items-start justify-between\">\n            <div className=\"flex-1 min-w-0\">\n              <CardTitle className=\"text-lg font-bold text-gray-900 truncate\">\n                {trip.title}\n              </CardTitle>\n              <CardDescription className=\"flex items-center mt-1 text-gray-600\">\n                <MapPinIcon className=\"h-4 w-4 mr-1 flex-shrink-0\" />\n                <span className=\"truncate\">\n                  {trip.destination_city ? `${trip.destination_city}, ` : ''}{trip.destination_country}\n                </span>\n              </CardDescription>\n            </div>\n            <Badge className={`${getTripTypeColor(trip.trip_type)} ml-2 flex-shrink-0`}>\n              {trip.trip_type}\n            </Badge>\n          </div>\n        </CardHeader>\n\n        <CardContent className=\"pt-0\">\n          {/* Trip Details */}\n          <div className=\"space-y-3\">\n            {/* Dates */}\n            <div className=\"flex items-center text-sm text-gray-600\">\n              <CalendarIcon className=\"h-4 w-4 mr-2 text-gray-400\" />\n              <span>{formatDate(trip.start_date)} - {formatDate(trip.end_date)}</span>\n              <ClockIcon className=\"h-4 w-4 ml-3 mr-1 text-gray-400\" />\n              <span>{calculateDuration()} days</span>\n            </div>\n\n            {/* Travelers */}\n            <div className=\"flex items-center text-sm text-gray-600\">\n              <UsersIcon className=\"h-4 w-4 mr-2 text-gray-400\" />\n              <span>{trip.traveler_count} {trip.traveler_count === 1 ? 'traveler' : 'travelers'}</span>\n            </div>\n\n            {/* Budget */}\n            {trip.budget_total && (\n              <div className=\"space-y-2\">\n                <div className=\"flex items-center justify-between text-sm\">\n                  <div className=\"flex items-center text-gray-600\">\n                    <DollarSignIcon className=\"h-4 w-4 mr-2 text-gray-400\" />\n                    <span>Budget</span>\n                  </div>\n                  <span className=\"font-medium text-gray-900\">\n                    {new Intl.NumberFormat('en-US', {\n                      style: 'currency',\n                      currency: trip.currency,\n                    }).format(trip.budget_spent)} / {new Intl.NumberFormat('en-US', {\n                      style: 'currency',\n                      currency: trip.currency,\n                    }).format(trip.budget_total)}\n                  </span>\n                </div>\n                \n                {/* Budget Progress Bar */}\n                <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                  <motion.div \n                    className={`h-2 rounded-full ${\n                      budgetProgress > 90 ? 'bg-red-500' : \n                      budgetProgress > 75 ? 'bg-yellow-500' : 'bg-green-500'\n                    }`}\n                    initial={{ width: 0 }}\n                    animate={{ width: `${budgetProgress}%` }}\n                    transition={{ duration: 1, delay: 0.5 }}\n                  />\n                </div>\n              </div>\n            )}\n\n            {/* Description */}\n            {trip.description && (\n              <p className=\"text-sm text-gray-600 line-clamp-2\">\n                {trip.description}\n              </p>\n            )}\n          </div>\n\n          {/* Action Buttons */}\n          <div className=\"flex items-center justify-between mt-4 pt-4 border-t border-gray-100\">\n            <Button variant=\"outline\" size=\"sm\" className=\"flex-1 mr-2\">\n              <Link href={`/trips/${trip.id}`} className=\"flex items-center justify-center w-full\">\n                View Details\n              </Link>\n            </Button>\n            \n            {onDelete && (\n              <Button \n                variant=\"outline\" \n                size=\"sm\" \n                className=\"text-red-600 hover:text-red-700 hover:bg-red-50\"\n                onClick={() => onDelete(trip.id)}\n              >\n                <TrashIcon className=\"h-4 w-4\" />\n              </Button>\n            )}\n          </div>\n        </CardContent>\n      </Card>\n    </motion.div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;;;AAnBA;;;;;;;;AA4Be,SAAS,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAiB;;IACjF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAe,OAAO;YAC3B,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAa,OAAO;YACzB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAS,OAAO;YACrB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,OAAO;YACP,KAAK;YACL,MAAM;QACR;IACF;IAEA,MAAM,oBAAoB;QACxB,MAAM,QAAQ,IAAI,KAAK,KAAK,UAAU;QACtC,MAAM,MAAM,IAAI,KAAK,KAAK,QAAQ;QAClC,MAAM,WAAW,KAAK,GAAG,CAAC,IAAI,OAAO,KAAK,MAAM,OAAO;QACvD,OAAO,KAAK,IAAI,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;IAClD;IAEA,MAAM,iBAAiB,KAAK,YAAY,GACpC,KAAK,GAAG,CAAC,AAAC,KAAK,YAAY,GAAG,KAAK,YAAY,GAAI,KAAK,OACxD;IAEJ,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,GAAG,CAAC;YAAG,OAAO;QAAK;QACjC,cAAc,IAAM,aAAa;QACjC,YAAY,IAAM,aAAa;QAC/B,WAAU;kBAEV,cAAA,6LAAC,mIAAA,CAAA,OAAI;YAAC,WAAU;;8BAEd,6LAAC;oBAAI,WAAU;;wBACZ,KAAK,eAAe,iBACnB,6LAAC;4BACC,KAAK,KAAK,eAAe;4BACzB,KAAK,KAAK,KAAK;4BACf,WAAU;;;;;iDAGZ,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,iNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;;;;;;sCAK1B,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;gCAAC,WAAW,GAAG,eAAe,KAAK,MAAM,EAAE,YAAY,CAAC;0CAC3D,KAAK,MAAM,CAAC,OAAO,CAAC,KAAK,KAAK,WAAW;;;;;;;;;;;wBAK7C,KAAK,SAAS,kBACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;gCAAC,WAAU;;kDACf,6LAAC,2MAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;sCAO5C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;4BAAE;4BACtB,SAAS;gCAAE,SAAS,YAAY,IAAI;4BAAE;4BACtC,YAAY;gCAAE,UAAU;4BAAI;;8CAE5B,6LAAC,qIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAK,SAAQ;oCAAY,WAAU;8CAC9C,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAM,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE;kDAC7B,cAAA,6LAAC,uMAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;;;;;;;;;;;gCAGtB,wBACC,6LAAC,qIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,WAAU;oCACV,SAAS,IAAM,OAAO;8CAEtB,cAAA,6LAAC,kNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;gCAGvB,yBACC,6LAAC,qIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,WAAU;oCACV,SAAS,IAAM,QAAQ,KAAK,EAAE;8CAE9B,cAAA,6LAAC,2MAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;8BAM7B,6LAAC,mIAAA,CAAA,aAAU;oBAAC,WAAU;8BACpB,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;kDAClB,KAAK,KAAK;;;;;;kDAEb,6LAAC,mIAAA,CAAA,kBAAe;wCAAC,WAAU;;0DACzB,6LAAC,iNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;0DACtB,6LAAC;gDAAK,WAAU;;oDACb,KAAK,gBAAgB,GAAG,GAAG,KAAK,gBAAgB,CAAC,EAAE,CAAC,GAAG;oDAAI,KAAK,mBAAmB;;;;;;;;;;;;;;;;;;;0CAI1F,6LAAC,oIAAA,CAAA,QAAK;gCAAC,WAAW,GAAG,iBAAiB,KAAK,SAAS,EAAE,mBAAmB,CAAC;0CACvE,KAAK,SAAS;;;;;;;;;;;;;;;;;8BAKrB,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;;sCAErB,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,iNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;sDACxB,6LAAC;;gDAAM,WAAW,KAAK,UAAU;gDAAE;gDAAI,WAAW,KAAK,QAAQ;;;;;;;sDAC/D,6LAAC,2MAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;sDACrB,6LAAC;;gDAAM;gDAAoB;;;;;;;;;;;;;8CAI7B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,2MAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;sDACrB,6LAAC;;gDAAM,KAAK,cAAc;gDAAC;gDAAE,KAAK,cAAc,KAAK,IAAI,aAAa;;;;;;;;;;;;;gCAIvE,KAAK,YAAY,kBAChB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,yNAAA,CAAA,iBAAc;4DAAC,WAAU;;;;;;sEAC1B,6LAAC;sEAAK;;;;;;;;;;;;8DAER,6LAAC;oDAAK,WAAU;;wDACb,IAAI,KAAK,YAAY,CAAC,SAAS;4DAC9B,OAAO;4DACP,UAAU,KAAK,QAAQ;wDACzB,GAAG,MAAM,CAAC,KAAK,YAAY;wDAAE;wDAAI,IAAI,KAAK,YAAY,CAAC,SAAS;4DAC9D,OAAO;4DACP,UAAU,KAAK,QAAQ;wDACzB,GAAG,MAAM,CAAC,KAAK,YAAY;;;;;;;;;;;;;sDAK/B,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAW,CAAC,iBAAiB,EAC3B,iBAAiB,KAAK,eACtB,iBAAiB,KAAK,kBAAkB,gBACxC;gDACF,SAAS;oDAAE,OAAO;gDAAE;gDACpB,SAAS;oDAAE,OAAO,GAAG,eAAe,CAAC,CAAC;gDAAC;gDACvC,YAAY;oDAAE,UAAU;oDAAG,OAAO;gDAAI;;;;;;;;;;;;;;;;;gCAO7C,KAAK,WAAW,kBACf,6LAAC;oCAAE,WAAU;8CACV,KAAK,WAAW;;;;;;;;;;;;sCAMvB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,MAAK;oCAAK,WAAU;8CAC5C,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAM,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE;wCAAE,WAAU;kDAA0C;;;;;;;;;;;gCAKtF,0BACC,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,SAAS,KAAK,EAAE;8CAE/B,cAAA,6LAAC,2MAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrC;GA/NwB;KAAA", "debugId": null}}, {"offset": {"line": 1469, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/components/ui/Modal.tsx"], "sourcesContent": ["'use client'\n\nimport { ReactNode, useEffect } from 'react'\nimport { X } from 'lucide-react'\nimport { cn } from '@/lib/utils'\n\ninterface ModalProps {\n  isOpen: boolean\n  onClose: () => void\n  title?: string\n  description?: string\n  children: ReactNode\n  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full'\n  className?: string\n}\n\nconst sizeClasses = {\n  sm: 'max-w-md',\n  md: 'max-w-lg',\n  lg: 'max-w-2xl',\n  xl: 'max-w-4xl',\n  full: 'max-w-7xl',\n}\n\nexport function Modal({\n  isOpen,\n  onClose,\n  title,\n  description,\n  children,\n  size = 'md',\n  className,\n}: ModalProps) {\n  useEffect(() => {\n    const handleEscape = (e: KeyboardEvent) => {\n      if (e.key === 'Escape') {\n        onClose()\n      }\n    }\n\n    if (isOpen) {\n      document.addEventListener('keydown', handleEscape)\n      document.body.style.overflow = 'hidden'\n    }\n\n    return () => {\n      document.removeEventListener('keydown', handleEscape)\n      document.body.style.overflow = 'unset'\n    }\n  }, [isOpen, onClose])\n\n  if (!isOpen) return null\n\n  return (\n    <div className=\"fixed inset-0 z-50 overflow-y-auto\">\n      <div className=\"flex min-h-full items-center justify-center p-4\">\n        {/* Backdrop */}\n        <div\n          className=\"fixed inset-0 bg-black bg-opacity-25 backdrop-blur-sm transition-opacity duration-300\"\n          onClick={onClose}\n        />\n\n        {/* Modal */}\n        <div\n          className={cn(\n            'relative w-full transform overflow-hidden rounded-2xl bg-white p-6 text-left shadow-xl transition-all duration-300 animate-slide-up',\n            sizeClasses[size],\n            className\n          )}\n          onClick={(e) => e.stopPropagation()}\n        >\n          {(title || description) && (\n            <div className=\"mb-6\">\n              <div className=\"flex items-center justify-between\">\n                {title && (\n                  <h3 className=\"text-lg font-semibold leading-6 text-gray-900\">\n                    {title}\n                  </h3>\n                )}\n                <button\n                  type=\"button\"\n                  className=\"rounded-md p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors\"\n                  onClick={onClose}\n                >\n                  <X className=\"h-5 w-5\" />\n                </button>\n              </div>\n              {description && (\n                <div className=\"mt-2\">\n                  <p className=\"text-sm text-gray-600\">{description}</p>\n                </div>\n              )}\n            </div>\n          )}\n\n          <div>{children}</div>\n        </div>\n      </div>\n    </div>\n  )\n}\n\ninterface ConfirmModalProps {\n  isOpen: boolean\n  onClose: () => void\n  onConfirm: () => void\n  title: string\n  message: string\n  confirmText?: string\n  cancelText?: string\n  variant?: 'danger' | 'warning' | 'info'\n}\n\nexport function ConfirmModal({\n  isOpen,\n  onClose,\n  onConfirm,\n  title,\n  message,\n  confirmText = 'Confirm',\n  cancelText = 'Cancel',\n  variant = 'info',\n}: ConfirmModalProps) {\n  const handleConfirm = () => {\n    onConfirm()\n    onClose()\n  }\n\n  const buttonVariant = variant === 'danger' ? 'destructive' : 'default'\n\n  return (\n    <Modal isOpen={isOpen} onClose={onClose} size=\"sm\">\n      <div className=\"text-center\">\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">{title}</h3>\n        <p className=\"text-gray-600 mb-6\">{message}</p>\n        <div className=\"flex justify-center space-x-3\">\n          <button\n            type=\"button\"\n            className=\"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors\"\n            onClick={onClose}\n          >\n            {cancelText}\n          </button>\n          <button\n            type=\"button\"\n            className={cn(\n              'px-4 py-2 text-sm font-medium text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors',\n              variant === 'danger'\n                ? 'bg-red-600 hover:bg-red-700 focus:ring-red-500'\n                : variant === 'warning'\n                ? 'bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500'\n                : 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500'\n            )}\n            onClick={handleConfirm}\n          >\n            {confirmText}\n          </button>\n        </div>\n      </div>\n    </Modal>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;;;AAJA;;;;AAgBA,MAAM,cAAc;IAClB,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,MAAM;AACR;AAEO,SAAS,MAAM,EACpB,MAAM,EACN,OAAO,EACP,KAAK,EACL,WAAW,EACX,QAAQ,EACR,OAAO,IAAI,EACX,SAAS,EACE;;IACX,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2BAAE;YACR,MAAM;gDAAe,CAAC;oBACpB,IAAI,EAAE,GAAG,KAAK,UAAU;wBACtB;oBACF;gBACF;;YAEA,IAAI,QAAQ;gBACV,SAAS,gBAAgB,CAAC,WAAW;gBACrC,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACjC;YAEA;mCAAO;oBACL,SAAS,mBAAmB,CAAC,WAAW;oBACxC,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;gBACjC;;QACF;0BAAG;QAAC;QAAQ;KAAQ;IAEpB,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBACC,WAAU;oBACV,SAAS;;;;;;8BAIX,6LAAC;oBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uIACA,WAAW,CAAC,KAAK,EACjB;oBAEF,SAAS,CAAC,IAAM,EAAE,eAAe;;wBAEhC,CAAC,SAAS,WAAW,mBACpB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;wCACZ,uBACC,6LAAC;4CAAG,WAAU;sDACX;;;;;;sDAGL,6LAAC;4CACC,MAAK;4CACL,WAAU;4CACV,SAAS;sDAET,cAAA,6LAAC,+LAAA,CAAA,IAAC;gDAAC,WAAU;;;;;;;;;;;;;;;;;gCAGhB,6BACC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAE,WAAU;kDAAyB;;;;;;;;;;;;;;;;;sCAM9C,6LAAC;sCAAK;;;;;;;;;;;;;;;;;;;;;;;AAKhB;GA5EgB;KAAA;AAyFT,SAAS,aAAa,EAC3B,MAAM,EACN,OAAO,EACP,SAAS,EACT,KAAK,EACL,OAAO,EACP,cAAc,SAAS,EACvB,aAAa,QAAQ,EACrB,UAAU,MAAM,EACE;IAClB,MAAM,gBAAgB;QACpB;QACA;IACF;IAEA,MAAM,gBAAgB,YAAY,WAAW,gBAAgB;IAE7D,qBACE,6LAAC;QAAM,QAAQ;QAAQ,SAAS;QAAS,MAAK;kBAC5C,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAA4C;;;;;;8BAC1D,6LAAC;oBAAE,WAAU;8BAAsB;;;;;;8BACnC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,MAAK;4BACL,WAAU;4BACV,SAAS;sCAER;;;;;;sCAEH,6LAAC;4BACC,MAAK;4BACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6HACA,YAAY,WACR,mDACA,YAAY,YACZ,4DACA;4BAEN,SAAS;sCAER;;;;;;;;;;;;;;;;;;;;;;;AAMb;MAhDgB", "debugId": null}}, {"offset": {"line": 1701, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/components/trip-planner/CreateTripModal.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { motion } from 'framer-motion'\nimport { CreateTripData } from '@/lib/services/trip-planner-service'\nimport { Modal } from '@/components/ui/Modal'\nimport { Button } from '@/components/ui/Button'\nimport { Input } from '@/components/ui/Input'\nimport { Badge } from '@/components/ui/Badge'\nimport { \n  MapPinIcon, \n  CalendarIcon, \n  DollarSignIcon, \n  UsersIcon,\n  ImageIcon,\n  FileTextIcon\n} from 'lucide-react'\n\ninterface CreateTripModalProps {\n  isOpen: boolean\n  onClose: () => void\n  onSubmit: (data: CreateTripData) => Promise<void>\n  loading?: boolean\n}\n\nconst tripTypes = [\n  { value: 'leisure', label: 'Leisure', color: 'bg-purple-100 text-purple-800' },\n  { value: 'business', label: 'Business', color: 'bg-blue-100 text-blue-800' },\n  { value: 'adventure', label: 'Adventure', color: 'bg-orange-100 text-orange-800' },\n  { value: 'family', label: 'Family', color: 'bg-green-100 text-green-800' },\n  { value: 'romantic', label: 'Romantic', color: 'bg-pink-100 text-pink-800' },\n  { value: 'solo', label: 'Solo', color: 'bg-indigo-100 text-indigo-800' },\n  { value: 'group', label: 'Group', color: 'bg-yellow-100 text-yellow-800' },\n]\n\nconst currencies = [\n  { value: 'USD', label: 'USD ($)' },\n  { value: 'EUR', label: 'EUR (€)' },\n  { value: 'GBP', label: 'GBP (£)' },\n  { value: 'JPY', label: 'JPY (¥)' },\n  { value: 'AUD', label: 'AUD (A$)' },\n  { value: 'CAD', label: 'CAD (C$)' },\n  { value: 'CHF', label: 'CHF (Fr)' },\n  { value: 'CNY', label: 'CNY (¥)' },\n]\n\nexport default function CreateTripModal({ isOpen, onClose, onSubmit, loading = false }: CreateTripModalProps) {\n  const [formData, setFormData] = useState<CreateTripData>({\n    title: '',\n    description: '',\n    destination_country: '',\n    destination_city: '',\n    start_date: '',\n    end_date: '',\n    trip_type: 'leisure',\n    budget_total: undefined,\n    currency: 'USD',\n    traveler_count: 1,\n    cover_image_url: '',\n    notes: ''\n  })\n\n  const [errors, setErrors] = useState<Record<string, string>>({})\n\n  const handleInputChange = (field: keyof CreateTripData, value: any) => {\n    setFormData(prev => ({ ...prev, [field]: value }))\n    if (errors[field]) {\n      setErrors(prev => ({ ...prev, [field]: '' }))\n    }\n  }\n\n  const validateForm = (): boolean => {\n    const newErrors: Record<string, string> = {}\n\n    if (!formData.title.trim()) {\n      newErrors.title = 'Trip title is required'\n    }\n\n    if (!formData.destination_country.trim()) {\n      newErrors.destination_country = 'Destination country is required'\n    }\n\n    if (!formData.start_date) {\n      newErrors.start_date = 'Start date is required'\n    }\n\n    if (!formData.end_date) {\n      newErrors.end_date = 'End date is required'\n    }\n\n    if (formData.start_date && formData.end_date) {\n      const startDate = new Date(formData.start_date)\n      const endDate = new Date(formData.end_date)\n      if (endDate <= startDate) {\n        newErrors.end_date = 'End date must be after start date'\n      }\n    }\n\n    if (formData.traveler_count < 1) {\n      newErrors.traveler_count = 'At least 1 traveler is required'\n    }\n\n    setErrors(newErrors)\n    return Object.keys(newErrors).length === 0\n  }\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    \n    if (!validateForm()) return\n\n    try {\n      await onSubmit(formData)\n      // Reset form\n      setFormData({\n        title: '',\n        description: '',\n        destination_country: '',\n        destination_city: '',\n        start_date: '',\n        end_date: '',\n        trip_type: 'leisure',\n        budget_total: undefined,\n        currency: 'USD',\n        traveler_count: 1,\n        cover_image_url: '',\n        notes: ''\n      })\n      setErrors({})\n      onClose()\n    } catch (error) {\n      console.error('Error creating trip:', error)\n    }\n  }\n\n  return (\n    <Modal isOpen={isOpen} onClose={onClose} size=\"lg\">\n      <div className=\"p-6\">\n        <div className=\"flex items-center mb-6\">\n          <div className=\"bg-blue-100 p-3 rounded-full mr-4\">\n            <MapPinIcon className=\"h-6 w-6 text-blue-600\" />\n          </div>\n          <div>\n            <h2 className=\"text-2xl font-bold text-gray-900\">Create New Trip</h2>\n            <p className=\"text-gray-600\">Plan your next adventure</p>\n          </div>\n        </div>\n\n        <form onSubmit={handleSubmit} className=\"space-y-6\">\n          {/* Basic Information */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div className=\"md:col-span-2\">\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Trip Title *\n              </label>\n              <Input\n                type=\"text\"\n                value={formData.title}\n                onChange={(e) => handleInputChange('title', e.target.value)}\n                placeholder=\"e.g., Summer Vacation in Europe\"\n                className={errors.title ? 'border-red-500' : ''}\n              />\n              {errors.title && <p className=\"text-red-500 text-sm mt-1\">{errors.title}</p>}\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Destination Country *\n              </label>\n              <Input\n                type=\"text\"\n                value={formData.destination_country}\n                onChange={(e) => handleInputChange('destination_country', e.target.value)}\n                placeholder=\"e.g., France\"\n                className={errors.destination_country ? 'border-red-500' : ''}\n              />\n              {errors.destination_country && <p className=\"text-red-500 text-sm mt-1\">{errors.destination_country}</p>}\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Destination City\n              </label>\n              <Input\n                type=\"text\"\n                value={formData.destination_city}\n                onChange={(e) => handleInputChange('destination_city', e.target.value)}\n                placeholder=\"e.g., Paris\"\n              />\n            </div>\n          </div>\n\n          {/* Dates */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                <CalendarIcon className=\"h-4 w-4 inline mr-1\" />\n                Start Date *\n              </label>\n              <Input\n                type=\"date\"\n                value={formData.start_date}\n                onChange={(e) => handleInputChange('start_date', e.target.value)}\n                className={errors.start_date ? 'border-red-500' : ''}\n              />\n              {errors.start_date && <p className=\"text-red-500 text-sm mt-1\">{errors.start_date}</p>}\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                <CalendarIcon className=\"h-4 w-4 inline mr-1\" />\n                End Date *\n              </label>\n              <Input\n                type=\"date\"\n                value={formData.end_date}\n                onChange={(e) => handleInputChange('end_date', e.target.value)}\n                className={errors.end_date ? 'border-red-500' : ''}\n              />\n              {errors.end_date && <p className=\"text-red-500 text-sm mt-1\">{errors.end_date}</p>}\n            </div>\n          </div>\n\n          {/* Trip Type */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-3\">\n              Trip Type\n            </label>\n            <div className=\"flex flex-wrap gap-2\">\n              {tripTypes.map((type) => (\n                <motion.button\n                  key={type.value}\n                  type=\"button\"\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                  onClick={() => handleInputChange('trip_type', type.value)}\n                  className={`px-3 py-2 rounded-lg text-sm font-medium transition-all ${\n                    formData.trip_type === type.value\n                      ? type.color + ' ring-2 ring-offset-2 ring-blue-500'\n                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'\n                  }`}\n                >\n                  {type.label}\n                </motion.button>\n              ))}\n            </div>\n          </div>\n\n          {/* Budget and Travelers */}\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                <DollarSignIcon className=\"h-4 w-4 inline mr-1\" />\n                Budget\n              </label>\n              <Input\n                type=\"number\"\n                value={formData.budget_total || ''}\n                onChange={(e) => handleInputChange('budget_total', e.target.value ? parseFloat(e.target.value) : undefined)}\n                placeholder=\"0.00\"\n                min=\"0\"\n                step=\"0.01\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Currency\n              </label>\n              <select\n                value={formData.currency}\n                onChange={(e) => handleInputChange('currency', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n              >\n                {currencies.map((currency) => (\n                  <option key={currency.value} value={currency.value}>\n                    {currency.label}\n                  </option>\n                ))}\n              </select>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                <UsersIcon className=\"h-4 w-4 inline mr-1\" />\n                Travelers *\n              </label>\n              <Input\n                type=\"number\"\n                value={formData.traveler_count}\n                onChange={(e) => handleInputChange('traveler_count', parseInt(e.target.value) || 1)}\n                min=\"1\"\n                className={errors.traveler_count ? 'border-red-500' : ''}\n              />\n              {errors.traveler_count && <p className=\"text-red-500 text-sm mt-1\">{errors.traveler_count}</p>}\n            </div>\n          </div>\n\n          {/* Description */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              <FileTextIcon className=\"h-4 w-4 inline mr-1\" />\n              Description\n            </label>\n            <textarea\n              value={formData.description}\n              onChange={(e) => handleInputChange('description', e.target.value)}\n              placeholder=\"Describe your trip plans...\"\n              rows={3}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n            />\n          </div>\n\n          {/* Cover Image */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              <ImageIcon className=\"h-4 w-4 inline mr-1\" />\n              Cover Image URL\n            </label>\n            <Input\n              type=\"url\"\n              value={formData.cover_image_url}\n              onChange={(e) => handleInputChange('cover_image_url', e.target.value)}\n              placeholder=\"https://example.com/image.jpg\"\n            />\n          </div>\n\n          {/* Action Buttons */}\n          <div className=\"flex justify-end space-x-3 pt-6 border-t border-gray-200\">\n            <Button\n              type=\"button\"\n              variant=\"outline\"\n              onClick={onClose}\n              disabled={loading}\n            >\n              Cancel\n            </Button>\n            <Button\n              type=\"submit\"\n              disabled={loading}\n              className=\"bg-blue-600 hover:bg-blue-700\"\n            >\n              {loading ? 'Creating...' : 'Create Trip'}\n            </Button>\n          </div>\n        </form>\n      </div>\n    </Modal>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AATA;;;;;;;AAyBA,MAAM,YAAY;IAChB;QAAE,OAAO;QAAW,OAAO;QAAW,OAAO;IAAgC;IAC7E;QAAE,OAAO;QAAY,OAAO;QAAY,OAAO;IAA4B;IAC3E;QAAE,OAAO;QAAa,OAAO;QAAa,OAAO;IAAgC;IACjF;QAAE,OAAO;QAAU,OAAO;QAAU,OAAO;IAA8B;IACzE;QAAE,OAAO;QAAY,OAAO;QAAY,OAAO;IAA4B;IAC3E;QAAE,OAAO;QAAQ,OAAO;QAAQ,OAAO;IAAgC;IACvE;QAAE,OAAO;QAAS,OAAO;QAAS,OAAO;IAAgC;CAC1E;AAED,MAAM,aAAa;IACjB;QAAE,OAAO;QAAO,OAAO;IAAU;IACjC;QAAE,OAAO;QAAO,OAAO;IAAU;IACjC;QAAE,OAAO;QAAO,OAAO;IAAU;IACjC;QAAE,OAAO;QAAO,OAAO;IAAU;IACjC;QAAE,OAAO;QAAO,OAAO;IAAW;IAClC;QAAE,OAAO;QAAO,OAAO;IAAW;IAClC;QAAE,OAAO;QAAO,OAAO;IAAW;IAClC;QAAE,OAAO;QAAO,OAAO;IAAU;CAClC;AAEc,SAAS,gBAAgB,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,UAAU,KAAK,EAAwB;;IAC1G,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;QACvD,OAAO;QACP,aAAa;QACb,qBAAqB;QACrB,kBAAkB;QAClB,YAAY;QACZ,UAAU;QACV,WAAW;QACX,cAAc;QACd,UAAU;QACV,gBAAgB;QAChB,iBAAiB;QACjB,OAAO;IACT;IAEA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAE9D,MAAM,oBAAoB,CAAC,OAA6B;QACtD,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;QAChD,IAAI,MAAM,CAAC,MAAM,EAAE;YACjB,UAAU,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,MAAM,EAAE;gBAAG,CAAC;QAC7C;IACF;IAEA,MAAM,eAAe;QACnB,MAAM,YAAoC,CAAC;QAE3C,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,IAAI;YAC1B,UAAU,KAAK,GAAG;QACpB;QAEA,IAAI,CAAC,SAAS,mBAAmB,CAAC,IAAI,IAAI;YACxC,UAAU,mBAAmB,GAAG;QAClC;QAEA,IAAI,CAAC,SAAS,UAAU,EAAE;YACxB,UAAU,UAAU,GAAG;QACzB;QAEA,IAAI,CAAC,SAAS,QAAQ,EAAE;YACtB,UAAU,QAAQ,GAAG;QACvB;QAEA,IAAI,SAAS,UAAU,IAAI,SAAS,QAAQ,EAAE;YAC5C,MAAM,YAAY,IAAI,KAAK,SAAS,UAAU;YAC9C,MAAM,UAAU,IAAI,KAAK,SAAS,QAAQ;YAC1C,IAAI,WAAW,WAAW;gBACxB,UAAU,QAAQ,GAAG;YACvB;QACF;QAEA,IAAI,SAAS,cAAc,GAAG,GAAG;YAC/B,UAAU,cAAc,GAAG;QAC7B;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB;QAErB,IAAI;YACF,MAAM,SAAS;YACf,aAAa;YACb,YAAY;gBACV,OAAO;gBACP,aAAa;gBACb,qBAAqB;gBACrB,kBAAkB;gBAClB,YAAY;gBACZ,UAAU;gBACV,WAAW;gBACX,cAAc;gBACd,UAAU;gBACV,gBAAgB;gBAChB,iBAAiB;gBACjB,OAAO;YACT;YACA,UAAU,CAAC;YACX;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;QACxC;IACF;IAEA,qBACE,6LAAC,oIAAA,CAAA,QAAK;QAAC,QAAQ;QAAQ,SAAS;QAAS,MAAK;kBAC5C,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,iNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;;;;;;sCAExB,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAmC;;;;;;8CACjD,6LAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;;;;;;;8BAIjC,6LAAC;oBAAK,UAAU;oBAAc,WAAU;;sCAEtC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,6LAAC,oIAAA,CAAA,QAAK;4CACJ,MAAK;4CACL,OAAO,SAAS,KAAK;4CACrB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;4CAC1D,aAAY;4CACZ,WAAW,OAAO,KAAK,GAAG,mBAAmB;;;;;;wCAE9C,OAAO,KAAK,kBAAI,6LAAC;4CAAE,WAAU;sDAA6B,OAAO,KAAK;;;;;;;;;;;;8CAGzE,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,6LAAC,oIAAA,CAAA,QAAK;4CACJ,MAAK;4CACL,OAAO,SAAS,mBAAmB;4CACnC,UAAU,CAAC,IAAM,kBAAkB,uBAAuB,EAAE,MAAM,CAAC,KAAK;4CACxE,aAAY;4CACZ,WAAW,OAAO,mBAAmB,GAAG,mBAAmB;;;;;;wCAE5D,OAAO,mBAAmB,kBAAI,6LAAC;4CAAE,WAAU;sDAA6B,OAAO,mBAAmB;;;;;;;;;;;;8CAGrG,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,6LAAC,oIAAA,CAAA,QAAK;4CACJ,MAAK;4CACL,OAAO,SAAS,gBAAgB;4CAChC,UAAU,CAAC,IAAM,kBAAkB,oBAAoB,EAAE,MAAM,CAAC,KAAK;4CACrE,aAAY;;;;;;;;;;;;;;;;;;sCAMlB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;;8DACf,6LAAC,iNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;gDAAwB;;;;;;;sDAGlD,6LAAC,oIAAA,CAAA,QAAK;4CACJ,MAAK;4CACL,OAAO,SAAS,UAAU;4CAC1B,UAAU,CAAC,IAAM,kBAAkB,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC/D,WAAW,OAAO,UAAU,GAAG,mBAAmB;;;;;;wCAEnD,OAAO,UAAU,kBAAI,6LAAC;4CAAE,WAAU;sDAA6B,OAAO,UAAU;;;;;;;;;;;;8CAGnF,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;;8DACf,6LAAC,iNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;gDAAwB;;;;;;;sDAGlD,6LAAC,oIAAA,CAAA,QAAK;4CACJ,MAAK;4CACL,OAAO,SAAS,QAAQ;4CACxB,UAAU,CAAC,IAAM,kBAAkB,YAAY,EAAE,MAAM,CAAC,KAAK;4CAC7D,WAAW,OAAO,QAAQ,GAAG,mBAAmB;;;;;;wCAEjD,OAAO,QAAQ,kBAAI,6LAAC;4CAAE,WAAU;sDAA6B,OAAO,QAAQ;;;;;;;;;;;;;;;;;;sCAKjF,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCAAI,WAAU;8CACZ,UAAU,GAAG,CAAC,CAAC,qBACd,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;4CAEZ,MAAK;4CACL,YAAY;gDAAE,OAAO;4CAAK;4CAC1B,UAAU;gDAAE,OAAO;4CAAK;4CACxB,SAAS,IAAM,kBAAkB,aAAa,KAAK,KAAK;4CACxD,WAAW,CAAC,wDAAwD,EAClE,SAAS,SAAS,KAAK,KAAK,KAAK,GAC7B,KAAK,KAAK,GAAG,wCACb,+CACJ;sDAED,KAAK,KAAK;2CAXN,KAAK,KAAK;;;;;;;;;;;;;;;;sCAkBvB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;;8DACf,6LAAC,yNAAA,CAAA,iBAAc;oDAAC,WAAU;;;;;;gDAAwB;;;;;;;sDAGpD,6LAAC,oIAAA,CAAA,QAAK;4CACJ,MAAK;4CACL,OAAO,SAAS,YAAY,IAAI;4CAChC,UAAU,CAAC,IAAM,kBAAkB,gBAAgB,EAAE,MAAM,CAAC,KAAK,GAAG,WAAW,EAAE,MAAM,CAAC,KAAK,IAAI;4CACjG,aAAY;4CACZ,KAAI;4CACJ,MAAK;;;;;;;;;;;;8CAIT,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,6LAAC;4CACC,OAAO,SAAS,QAAQ;4CACxB,UAAU,CAAC,IAAM,kBAAkB,YAAY,EAAE,MAAM,CAAC,KAAK;4CAC7D,WAAU;sDAET,WAAW,GAAG,CAAC,CAAC,yBACf,6LAAC;oDAA4B,OAAO,SAAS,KAAK;8DAC/C,SAAS,KAAK;mDADJ,SAAS,KAAK;;;;;;;;;;;;;;;;8CAOjC,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;;8DACf,6LAAC,2MAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;gDAAwB;;;;;;;sDAG/C,6LAAC,oIAAA,CAAA,QAAK;4CACJ,MAAK;4CACL,OAAO,SAAS,cAAc;4CAC9B,UAAU,CAAC,IAAM,kBAAkB,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;4CACjF,KAAI;4CACJ,WAAW,OAAO,cAAc,GAAG,mBAAmB;;;;;;wCAEvD,OAAO,cAAc,kBAAI,6LAAC;4CAAE,WAAU;sDAA6B,OAAO,cAAc;;;;;;;;;;;;;;;;;;sCAK7F,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;;sDACf,6LAAC,qNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;wCAAwB;;;;;;;8CAGlD,6LAAC;oCACC,OAAO,SAAS,WAAW;oCAC3B,UAAU,CAAC,IAAM,kBAAkB,eAAe,EAAE,MAAM,CAAC,KAAK;oCAChE,aAAY;oCACZ,MAAM;oCACN,WAAU;;;;;;;;;;;;sCAKd,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;;sDACf,6LAAC,2MAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAAwB;;;;;;;8CAG/C,6LAAC,oIAAA,CAAA,QAAK;oCACJ,MAAK;oCACL,OAAO,SAAS,eAAe;oCAC/B,UAAU,CAAC,IAAM,kBAAkB,mBAAmB,EAAE,MAAM,CAAC,KAAK;oCACpE,aAAY;;;;;;;;;;;;sCAKhB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,SAAS;oCACT,UAAU;8CACX;;;;;;8CAGD,6LAAC,qIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,UAAU;oCACV,WAAU;8CAET,UAAU,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOzC;GA/SwB;KAAA", "debugId": null}}, {"offset": {"line": 2431, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/app/trips/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { motion, AnimatePresence } from 'framer-motion'\nimport { tripPlannerService, type Trip, type CreateTripData } from '@/lib/services/trip-planner-service'\nimport { Button } from '@/components/ui/Button'\nimport { Input } from '@/components/ui/Input'\nimport { Loading } from '@/components/ui/Loading'\nimport TripCard from '@/components/trip-planner/TripCard'\nimport CreateTripModal from '@/components/trip-planner/CreateTripModal'\nimport { \n  PlusIcon, \n  SearchIcon, \n  FilterIcon, \n  MapIcon, \n  ListIcon,\n  CalendarIcon,\n  DollarSignIcon,\n  TrendingUpIcon,\n  GlobeIcon,\n  UsersIcon\n} from 'lucide-react'\nimport toast from 'react-hot-toast'\n\nexport default function TripsPage() {\n  const [trips, setTrips] = useState<Trip[]>([])\n  const [loading, setLoading] = useState(true)\n  const [creating, setCreating] = useState(false)\n  const [showCreateModal, setShowCreateModal] = useState(false)\n  const [searchTerm, setSearchTerm] = useState('')\n  const [filterStatus, setFilterStatus] = useState<string>('all')\n  const [filterType, setFilterType] = useState<string>('all')\n  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')\n\n  useEffect(() => {\n    fetchTrips()\n  }, [])\n\n  const fetchTrips = async () => {\n    try {\n      const data = await tripPlannerService.getTrips()\n      setTrips(data)\n    } catch (error) {\n      console.error('Error fetching trips:', error)\n      toast.error('Failed to load trips')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleCreateTrip = async (data: CreateTripData) => {\n    setCreating(true)\n    try {\n      const newTrip = await tripPlannerService.createTrip(data)\n      setTrips([newTrip, ...trips])\n      toast.success('Trip created successfully!')\n    } catch (error) {\n      console.error('Error creating trip:', error)\n      toast.error('Failed to create trip')\n      throw error\n    } finally {\n      setCreating(false)\n    }\n  }\n\n  const handleDeleteTrip = async (tripId: string) => {\n    if (!confirm('Are you sure you want to delete this trip?')) return\n\n    try {\n      await tripPlannerService.deleteTrip(tripId)\n      setTrips(trips.filter(trip => trip.id !== tripId))\n      toast.success('Trip deleted successfully!')\n    } catch (error) {\n      console.error('Error deleting trip:', error)\n      toast.error('Failed to delete trip')\n    }\n  }\n\n  const handleShareTrip = (tripId: string) => {\n    // TODO: Implement sharing functionality\n    toast.info('Sharing functionality coming soon!')\n  }\n\n  // Filter trips based on search and filters\n  const filteredTrips = trips.filter(trip => {\n    const matchesSearch = trip.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         trip.destination_country.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         trip.destination_city?.toLowerCase().includes(searchTerm.toLowerCase())\n    \n    const matchesStatus = filterStatus === 'all' || trip.status === filterStatus\n    const matchesType = filterType === 'all' || trip.trip_type === filterType\n\n    return matchesSearch && matchesStatus && matchesType\n  })\n\n  // Calculate statistics\n  const stats = {\n    totalTrips: trips.length,\n    upcomingTrips: trips.filter(trip => new Date(trip.start_date) > new Date()).length,\n    totalBudget: trips.reduce((sum, trip) => sum + (trip.budget_total || 0), 0),\n    totalSpent: trips.reduce((sum, trip) => sum + trip.budget_spent, 0),\n    countries: new Set(trips.map(trip => trip.destination_country)).size\n  }\n\n  if (loading) {\n    return <Loading size=\"lg\" text=\"Loading your trips...\" className=\"h-64\" />\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <motion.div\n        initial={{ opacity: 0, y: -20 }}\n        animate={{ opacity: 1, y: 0 }}\n        className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\"\n      >\n        <div>\n          <h1 className=\"text-3xl font-bold text-gray-900 flex items-center\">\n            <GlobeIcon className=\"h-8 w-8 mr-3 text-blue-600\" />\n            Trip Planner\n          </h1>\n          <p className=\"mt-2 text-gray-600\">\n            Plan, organize, and track your adventures around the world\n          </p>\n        </div>\n        <div className=\"flex items-center space-x-3\">\n          {/* View Mode Toggle */}\n          <div className=\"flex items-center bg-gray-100 rounded-lg p-1\">\n            <Button\n              variant={viewMode === 'grid' ? 'default' : 'ghost'}\n              size=\"sm\"\n              onClick={() => setViewMode('grid')}\n              className=\"h-8\"\n            >\n              <ListIcon className=\"h-4 w-4\" />\n            </Button>\n            <Button\n              variant={viewMode === 'list' ? 'default' : 'ghost'}\n              size=\"sm\"\n              onClick={() => setViewMode('list')}\n              className=\"h-8\"\n            >\n              <MapIcon className=\"h-4 w-4\" />\n            </Button>\n          </div>\n\n          <Button\n            onClick={() => setShowCreateModal(true)}\n            className=\"bg-blue-600 hover:bg-blue-700\"\n          >\n            <PlusIcon className=\"h-4 w-4 mr-2\" />\n            New Trip\n          </Button>\n        </div>\n      </motion.div>\n\n      {/* Statistics Cards */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ delay: 0.1 }}\n        className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4\"\n      >\n        <div className=\"bg-white p-4 rounded-lg shadow-sm border border-gray-200\">\n          <div className=\"flex items-center\">\n            <div className=\"bg-blue-100 p-2 rounded-lg\">\n              <GlobeIcon className=\"h-5 w-5 text-blue-600\" />\n            </div>\n            <div className=\"ml-3\">\n              <p className=\"text-sm font-medium text-gray-600\">Total Trips</p>\n              <p className=\"text-2xl font-bold text-gray-900\">{stats.totalTrips}</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white p-4 rounded-lg shadow-sm border border-gray-200\">\n          <div className=\"flex items-center\">\n            <div className=\"bg-green-100 p-2 rounded-lg\">\n              <CalendarIcon className=\"h-5 w-5 text-green-600\" />\n            </div>\n            <div className=\"ml-3\">\n              <p className=\"text-sm font-medium text-gray-600\">Upcoming</p>\n              <p className=\"text-2xl font-bold text-gray-900\">{stats.upcomingTrips}</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white p-4 rounded-lg shadow-sm border border-gray-200\">\n          <div className=\"flex items-center\">\n            <div className=\"bg-purple-100 p-2 rounded-lg\">\n              <MapIcon className=\"h-5 w-5 text-purple-600\" />\n            </div>\n            <div className=\"ml-3\">\n              <p className=\"text-sm font-medium text-gray-600\">Countries</p>\n              <p className=\"text-2xl font-bold text-gray-900\">{stats.countries}</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white p-4 rounded-lg shadow-sm border border-gray-200\">\n          <div className=\"flex items-center\">\n            <div className=\"bg-yellow-100 p-2 rounded-lg\">\n              <DollarSignIcon className=\"h-5 w-5 text-yellow-600\" />\n            </div>\n            <div className=\"ml-3\">\n              <p className=\"text-sm font-medium text-gray-600\">Total Budget</p>\n              <p className=\"text-2xl font-bold text-gray-900\">\n                ${stats.totalBudget.toLocaleString()}\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white p-4 rounded-lg shadow-sm border border-gray-200\">\n          <div className=\"flex items-center\">\n            <div className=\"bg-red-100 p-2 rounded-lg\">\n              <TrendingUpIcon className=\"h-5 w-5 text-red-600\" />\n            </div>\n            <div className=\"ml-3\">\n              <p className=\"text-sm font-medium text-gray-600\">Total Spent</p>\n              <p className=\"text-2xl font-bold text-gray-900\">\n                ${stats.totalSpent.toLocaleString()}\n              </p>\n            </div>\n          </div>\n        </div>\n      </motion.div>\n\n      {/* Search and Filters */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ delay: 0.2 }}\n        className=\"flex flex-col sm:flex-row gap-4 bg-white p-4 rounded-lg shadow-sm border border-gray-200\"\n      >\n        <div className=\"flex-1\">\n          <div className=\"relative\">\n            <SearchIcon className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n            <Input\n              type=\"text\"\n              placeholder=\"Search trips by title, destination...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"pl-10\"\n            />\n          </div>\n        </div>\n\n        <div className=\"flex gap-3\">\n          <select\n            value={filterStatus}\n            onChange={(e) => setFilterStatus(e.target.value)}\n            className=\"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n          >\n            <option value=\"all\">All Status</option>\n            <option value=\"planning\">Planning</option>\n            <option value=\"booked\">Booked</option>\n            <option value=\"in_progress\">In Progress</option>\n            <option value=\"completed\">Completed</option>\n            <option value=\"cancelled\">Cancelled</option>\n          </select>\n\n          <select\n            value={filterType}\n            onChange={(e) => setFilterType(e.target.value)}\n            className=\"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n          >\n            <option value=\"all\">All Types</option>\n            <option value=\"leisure\">Leisure</option>\n            <option value=\"business\">Business</option>\n            <option value=\"adventure\">Adventure</option>\n            <option value=\"family\">Family</option>\n            <option value=\"romantic\">Romantic</option>\n            <option value=\"solo\">Solo</option>\n            <option value=\"group\">Group</option>\n          </select>\n        </div>\n      </motion.div>\n\n      {/* Trips Grid */}\n      <AnimatePresence mode=\"wait\">\n        {filteredTrips.length === 0 ? (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            className=\"text-center py-12\"\n          >\n            <GlobeIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\n            <h3 className=\"mt-2 text-sm font-medium text-gray-900\">\n              {trips.length === 0 ? 'No trips yet' : 'No trips match your filters'}\n            </h3>\n            <p className=\"mt-1 text-sm text-gray-500\">\n              {trips.length === 0 \n                ? 'Get started by creating your first trip.'\n                : 'Try adjusting your search criteria or filters.'\n              }\n            </p>\n            {trips.length === 0 && (\n              <div className=\"mt-6\">\n                <Button\n                  onClick={() => setShowCreateModal(true)}\n                  className=\"bg-blue-600 hover:bg-blue-700\"\n                >\n                  <PlusIcon className=\"h-4 w-4 mr-2\" />\n                  Create Your First Trip\n                </Button>\n              </div>\n            )}\n          </motion.div>\n        ) : (\n          <motion.div\n            key={viewMode}\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            className={viewMode === 'grid' \n              ? 'grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'\n              : 'space-y-4'\n            }\n          >\n            {filteredTrips.map((trip, index) => (\n              <motion.div\n                key={trip.id}\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: index * 0.05 }}\n              >\n                <TripCard\n                  trip={trip}\n                  onDelete={handleDeleteTrip}\n                  onShare={handleShareTrip}\n                />\n              </motion.div>\n            ))}\n          </motion.div>\n        )}\n      </AnimatePresence>\n\n      {/* Create Trip Modal */}\n      <CreateTripModal\n        isOpen={showCreateModal}\n        onClose={() => setShowCreateModal(false)}\n        onSubmit={handleCreateTrip}\n        loading={creating}\n      />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;;;AAtBA;;;;;;;;;;;AAwBe,SAAS;;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACzD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACrD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;IAE1D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR;QACF;8BAAG,EAAE;IAEL,MAAM,aAAa;QACjB,IAAI;YACF,MAAM,OAAO,MAAM,uJAAA,CAAA,qBAAkB,CAAC,QAAQ;YAC9C,SAAS;QACX,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,YAAY;QACZ,IAAI;YACF,MAAM,UAAU,MAAM,uJAAA,CAAA,qBAAkB,CAAC,UAAU,CAAC;YACpD,SAAS;gBAAC;mBAAY;aAAM;YAC5B,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACZ,MAAM;QACR,SAAU;YACR,YAAY;QACd;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI,CAAC,QAAQ,+CAA+C;QAE5D,IAAI;YACF,MAAM,uJAAA,CAAA,qBAAkB,CAAC,UAAU,CAAC;YACpC,SAAS,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;YAC1C,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,wCAAwC;QACxC,0JAAA,CAAA,UAAK,CAAC,IAAI,CAAC;IACb;IAEA,2CAA2C;IAC3C,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA;QACjC,MAAM,gBAAgB,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACzD,KAAK,mBAAmB,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACtE,KAAK,gBAAgB,EAAE,cAAc,SAAS,WAAW,WAAW;QAEzF,MAAM,gBAAgB,iBAAiB,SAAS,KAAK,MAAM,KAAK;QAChE,MAAM,cAAc,eAAe,SAAS,KAAK,SAAS,KAAK;QAE/D,OAAO,iBAAiB,iBAAiB;IAC3C;IAEA,uBAAuB;IACvB,MAAM,QAAQ;QACZ,YAAY,MAAM,MAAM;QACxB,eAAe,MAAM,MAAM,CAAC,CAAA,OAAQ,IAAI,KAAK,KAAK,UAAU,IAAI,IAAI,QAAQ,MAAM;QAClF,aAAa,MAAM,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,CAAC,KAAK,YAAY,IAAI,CAAC,GAAG;QACzE,YAAY,MAAM,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,YAAY,EAAE;QACjE,WAAW,IAAI,IAAI,MAAM,GAAG,CAAC,CAAA,OAAQ,KAAK,mBAAmB,GAAG,IAAI;IACtE;IAEA,IAAI,SAAS;QACX,qBAAO,6LAAC,sIAAA,CAAA,UAAO;YAAC,MAAK;YAAK,MAAK;YAAwB,WAAU;;;;;;IACnE;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG,CAAC;gBAAG;gBAC9B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,WAAU;;kCAEV,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC,2MAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAA+B;;;;;;;0CAGtD,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;kCAIpC,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAS,aAAa,SAAS,YAAY;wCAC3C,MAAK;wCACL,SAAS,IAAM,YAAY;wCAC3B,WAAU;kDAEV,cAAA,6LAAC,yMAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAEtB,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAS,aAAa,SAAS,YAAY;wCAC3C,MAAK;wCACL,SAAS,IAAM,YAAY;wCAC3B,WAAU;kDAEV,cAAA,6LAAC,uMAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAIvB,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAS,IAAM,mBAAmB;gCAClC,WAAU;;kDAEV,6LAAC,yMAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;0BAO3C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,OAAO;gBAAI;gBACzB,WAAU;;kCAEV,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,2MAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;8CAEvB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDAAoC,MAAM,UAAU;;;;;;;;;;;;;;;;;;;;;;;kCAKvE,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,iNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;;;;;;8CAE1B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDAAoC,MAAM,aAAa;;;;;;;;;;;;;;;;;;;;;;;kCAK1E,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,uMAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;;;;;;8CAErB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDAAoC,MAAM,SAAS;;;;;;;;;;;;;;;;;;;;;;;kCAKtE,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,yNAAA,CAAA,iBAAc;wCAAC,WAAU;;;;;;;;;;;8CAE5B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;;gDAAmC;gDAC5C,MAAM,WAAW,CAAC,cAAc;;;;;;;;;;;;;;;;;;;;;;;;kCAM1C,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,yNAAA,CAAA,iBAAc;wCAAC,WAAU;;;;;;;;;;;8CAE5B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;;gDAAmC;gDAC5C,MAAM,UAAU,CAAC,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ3C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,OAAO;gBAAI;gBACzB,WAAU;;kCAEV,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,6MAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;8CACtB,6LAAC,oIAAA,CAAA,QAAK;oCACJ,MAAK;oCACL,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oCAC7C,WAAU;;;;;;;;;;;;;;;;;kCAKhB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;gCAC/C,WAAU;;kDAEV,6LAAC;wCAAO,OAAM;kDAAM;;;;;;kDACpB,6LAAC;wCAAO,OAAM;kDAAW;;;;;;kDACzB,6LAAC;wCAAO,OAAM;kDAAS;;;;;;kDACvB,6LAAC;wCAAO,OAAM;kDAAc;;;;;;kDAC5B,6LAAC;wCAAO,OAAM;kDAAY;;;;;;kDAC1B,6LAAC;wCAAO,OAAM;kDAAY;;;;;;;;;;;;0CAG5B,6LAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gCAC7C,WAAU;;kDAEV,6LAAC;wCAAO,OAAM;kDAAM;;;;;;kDACpB,6LAAC;wCAAO,OAAM;kDAAU;;;;;;kDACxB,6LAAC;wCAAO,OAAM;kDAAW;;;;;;kDACzB,6LAAC;wCAAO,OAAM;kDAAY;;;;;;kDAC1B,6LAAC;wCAAO,OAAM;kDAAS;;;;;;kDACvB,6LAAC;wCAAO,OAAM;kDAAW;;;;;;kDACzB,6LAAC;wCAAO,OAAM;kDAAO;;;;;;kDACrB,6LAAC;wCAAO,OAAM;kDAAQ;;;;;;;;;;;;;;;;;;;;;;;;0BAM5B,6LAAC,4LAAA,CAAA,kBAAe;gBAAC,MAAK;0BACnB,cAAc,MAAM,KAAK,kBACxB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,WAAU;;sCAEV,6LAAC,2MAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;sCACrB,6LAAC;4BAAG,WAAU;sCACX,MAAM,MAAM,KAAK,IAAI,iBAAiB;;;;;;sCAEzC,6LAAC;4BAAE,WAAU;sCACV,MAAM,MAAM,KAAK,IACd,6CACA;;;;;;wBAGL,MAAM,MAAM,KAAK,mBAChB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAS,IAAM,mBAAmB;gCAClC,WAAU;;kDAEV,6LAAC,yMAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;yCAO7C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBAET,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,WAAW,aAAa,SACpB,wEACA;8BAGH,cAAc,GAAG,CAAC,CAAC,MAAM,sBACxB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,OAAO,QAAQ;4BAAK;sCAElC,cAAA,6LAAC,oJAAA,CAAA,UAAQ;gCACP,MAAM;gCACN,UAAU;gCACV,SAAS;;;;;;2BARN,KAAK,EAAE;;;;;mBAVX;;;;;;;;;;0BA2BX,6LAAC,2JAAA,CAAA,UAAe;gBACd,QAAQ;gBACR,SAAS,IAAM,mBAAmB;gBAClC,UAAU;gBACV,SAAS;;;;;;;;;;;;AAIjB;GAlUwB;KAAA", "debugId": null}}]}